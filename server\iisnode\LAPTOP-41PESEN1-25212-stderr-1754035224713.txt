[2025-08-01 08:00:45] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:45] [ERROR] SQL Server数据库连接测试失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:45] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:45] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:45] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)"}
[2025-08-01 08:00:45] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:45] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:45] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)"}
[2025-08-01 08:00:47] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:47] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:47] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:47] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":500,"duration":"4076ms"}
[2025-08-01 08:00:47] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"4077ms","threshold":"2000ms"}
[2025-08-01 08:00:47] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:47] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:47] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:47] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":500,"duration":"3060ms"}
[2025-08-01 08:00:47] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3061ms","threshold":"2000ms"}
[2025-08-01 08:00:49] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:49] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:49] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)"}
[2025-08-01 08:00:51] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:51] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:51] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:51] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"4095ms"}
[2025-08-01 08:00:51] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"4096ms","threshold":"2000ms"}
[2025-08-01 08:00:53] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:53] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:53] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)"}
[2025-08-01 08:00:55] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:55] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:55] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:55] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"4132ms"}
[2025-08-01 08:00:55] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"4133ms","threshold":"2000ms"}
[2025-08-01 08:01:09] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:09] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:01:09] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)"}
[2025-08-01 08:01:11] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:11] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:01:11] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:11] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":500,"duration":"4078ms"}
[2025-08-01 08:01:11] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"4079ms","threshold":"2000ms"}
[2025-08-01 08:01:13] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:13] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:01:13] [WARN] 原始表查询失败，尝试使用备用表 | {"error":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)"}
[2025-08-01 08:01:15] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:15] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:01:15] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:15] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":500,"duration":"4098ms"}
[2025-08-01 08:01:15] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"4099ms","threshold":"2000ms"}
[2025-08-01 08:02:14] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"40861ms"}
[2025-08-01 08:02:14] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"40862ms","threshold":"2000ms"}
[2025-08-01 08:02:15] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"18120ms"}
[2025-08-01 08:02:15] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"18122ms","threshold":"2000ms"}
[2025-08-01 08:02:17] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"14595ms"}
[2025-08-01 08:02:17] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"14596ms","threshold":"2000ms"}
[2025-08-01 08:02:19] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"4590ms"}
[2025-08-01 08:02:19] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"4591ms","threshold":"2000ms"}
[2025-08-01 08:02:20] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":200,"duration":"4577ms"}
[2025-08-01 08:02:20] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"4578ms","threshold":"2000ms"}
[2025-08-01 08:02:22] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2082ms"}
[2025-08-01 08:02:22] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2084ms","threshold":"2000ms"}
[2025-08-01 08:02:24] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"2437ms"}
[2025-08-01 08:02:24] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"2438ms","threshold":"2000ms"}
[2025-08-01 08:02:26] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":200,"duration":"3698ms"}
[2025-08-01 08:02:26] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"3699ms","threshold":"2000ms"}
[2025-08-01 08:02:28] [WARN] 慢请求检测 | {"method":"GET","url":"/","statusCode":304,"duration":"2129ms"}
[2025-08-01 08:02:28] [WARN] 检测到慢请求 | {"method":"GET","url":"/","duration":"2130ms","threshold":"2000ms"}
[2025-08-01 08:02:30] [WARN] 慢请求检测 | {"method":"GET","url":"/?keyword=","statusCode":304,"duration":"3755ms"}
[2025-08-01 08:02:30] [WARN] 检测到慢请求 | {"method":"GET","url":"/?keyword=","duration":"3756ms","threshold":"2000ms"}
[2025-08-01 08:02:48] [WARN] 慢请求检测 | {"method":"GET","url":"/project/B05AJA220003","statusCode":200,"duration":"12126ms"}
[2025-08-01 08:02:48] [WARN] 检测到慢请求 | {"method":"GET","url":"/project/B05AJA220003","duration":"12128ms","threshold":"2000ms"}
[2025-08-01 08:45:38] [WARN] 慢请求检测 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"18760ms"}
[2025-08-01 08:45:38] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"18761ms","threshold":"2000ms"}
[2025-08-01 08:46:46] [WARN] 慢请求检测 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16446ms"}
[2025-08-01 08:46:46] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16447ms","threshold":"2000ms"}
[2025-08-01 08:47:56] [WARN] 慢请求检测 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"16110ms"}
[2025-08-01 08:47:56] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"16112ms","threshold":"2000ms"}
[2025-08-01 08:56:27] [WARN] 慢请求检测 | {"method":"GET","url":"/B05-2200252","statusCode":304,"duration":"2551ms"}
[2025-08-01 08:56:27] [WARN] 检测到慢请求 | {"method":"GET","url":"/B05-2200252","duration":"2552ms","threshold":"2000ms"}
