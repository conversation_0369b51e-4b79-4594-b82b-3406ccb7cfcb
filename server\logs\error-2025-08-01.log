[2025-08-01 08:00:45] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:45] [ERROR] SQL Server数据库连接测试失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:45] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:45] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:45] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:45] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:47] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:47] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:47] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:47] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:47] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:47] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:49] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:49] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:51] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:51] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:51] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:53] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:53] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:55] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:00:55] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:00:55] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:09] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:09] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:01:11] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:11] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:01:11] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:13] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:13] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            cp.ProjectId as id,\n            cp.ProjectName as name,\n            cp.ProjectId as code,\n            cp.X_ConsUnitId as construction_unit_id,\n            ISNULL(cbp.BizPartnerName, cp.X_ConsUnitId) as construction_unit,\n            cp.X_OrgId as company_id,\n            cp.X_ProImplement as implementation_status\n          FROM dbo.comProject cp\n          LEFT JOIN dbo.comBusinessPartner cbp ON cp.X_ConsUnitId = cbp.BizPartnerId\n          WHERE cp.X_OrgId = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:01:15] [ERROR] SQL Server连接池创建失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
[2025-08-01 08:01:15] [ERROR] SQL Server查询执行失败 | {"query":"\n          SELECT\n            id,\n            name,\n            code,\n            construction_unit_id,\n            construction_unit,\n            company_id,\n            implementation_status,\n            0 as task_count,\n            0 as supplying_count,\n            0 as completed_count,\n            0 as feedback_count\n          FROM dbo.backup_projects\n          WHERE company_id = ?\n        ","params":["1007"],"error":{"code":"ESOCKET","originalError":{"code":"ESOCKET"},"name":"ConnectionError"}}
[2025-08-01 08:01:15] [ERROR] 获取公司工程列表失败 | {"name":"ConnectionError","message":"Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)","stack":"ConnectionError: Failed to connect to 192.168.16.94:1433 - Could not connect (sequence)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\mssql\\lib\\tedious\\connection-pool.js:85:17\n    at Connection.onConnect (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:838:9)\n    at Object.onceWrapper (node:events:633:26)\n    at Connection.emit (node:events:518:28)\n    at Connection.emit (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:959:18)\n    at Connection.socketError (D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1340:12)\n    at D:\\Code-workspace\\xt\\server\\node_modules\\tedious\\lib\\connection.js:1133:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:85:11)"}
