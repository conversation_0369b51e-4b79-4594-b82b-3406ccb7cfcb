/**
 * 全局错误处理管理器
 * 提供统一的错误处理、分类、用户友好提示和错误上报功能
 */

class ErrorManager {
  constructor() {
    // 错误类型定义
    this.ERROR_TYPES = {
      NETWORK: 'network',           // 网络错误
      AUTH: 'auth',                 // 认证错误
      VALIDATION: 'validation',     // 验证错误
      BUSINESS: 'business',         // 业务逻辑错误
      SYSTEM: 'system',             // 系统错误
      UNKNOWN: 'unknown'            // 未知错误
    };

    // 错误级别
    this.ERROR_LEVELS = {
      LOW: 'low',                   // 低级别（用户可忽略）
      MEDIUM: 'medium',             // 中级别（需要用户注意）
      HIGH: 'high',                 // 高级别（严重错误）
      CRITICAL: 'critical'          // 关键级别（系统级错误）
    };

    // 错误消息映射
    this.errorMessages = {
      // 网络错误
      'network_timeout': '网络连接超时，请检查网络后重试',
      'network_offline': '网络连接不可用，请检查网络设置',
      'network_error': '网络错误，请稍后重试',
      'server_error': '服务器错误，请稍后重试',
      'request_failed': '请求失败，请重试',
      
      // 认证错误
      'auth_failed': '登录已过期，请重新登录',
      'permission_denied': '权限不足，无法执行此操作',
      'token_expired': '登录状态已过期，请重新登录',
      'invalid_credentials': '用户名或密码错误',
      
      // 验证错误
      'validation_failed': '输入信息有误，请检查后重试',
      'required_field': '请填写必填信息',
      'invalid_format': '输入格式不正确',
      'file_too_large': '文件大小超出限制',
      'file_type_error': '文件类型不支持',
      
      // 业务错误
      'business_rule_violation': '操作违反业务规则',
      'resource_not_found': '请求的资源不存在',
      'operation_not_allowed': '当前状态下不允许此操作',
      'quota_exceeded': '已超出配额限制',
      
      // 系统错误
      'system_error': '系统错误，请联系管理员',
      'database_error': '数据库错误，请稍后重试',
      'service_unavailable': '服务暂时不可用，请稍后重试'
    };

    // 错误统计
    this.errorStats = {
      total: 0,
      byType: {},
      byLevel: {},
      recent: []
    };

    // 配置
    this.config = {
      // 是否启用错误上报
      enableReporting: true,
      // 是否显示详细错误信息（开发模式）
      showDetailedError: false,
      // 最大错误记录数
      maxErrorRecords: 100,
      // 错误上报URL
      reportUrl: '/api/error-report',
      // 自动重试配置
      autoRetry: {
        enabled: true,
        maxRetries: 3,
        retryDelay: 1000
      }
    };
  }

  /**
   * 处理错误
   * @param {Error|Object} error - 错误对象
   * @param {Object} context - 错误上下文
   * @param {Object} options - 处理选项
   */
  handleError(error, context = {}, options = {}) {
    const errorInfo = this.parseError(error, context);
    
    // 记录错误
    this.recordError(errorInfo);
    
    // 显示用户友好的错误提示
    if (options.showToUser !== false) {
      this.showErrorToUser(errorInfo, options);
    }
    
    // 上报错误
    if (this.config.enableReporting && errorInfo.level !== this.ERROR_LEVELS.LOW) {
      this.reportError(errorInfo);
    }
    
    return errorInfo;
  }

  /**
   * 解析错误信息
   * @private
   * @param {Error|Object} error - 错误对象
   * @param {Object} context - 错误上下文
   * @returns {Object} 解析后的错误信息
   */
  parseError(error, context) {
    let errorInfo = {
      id: this.generateErrorId(),
      timestamp: new Date().toISOString(),
      type: this.ERROR_TYPES.UNKNOWN,
      level: this.ERROR_LEVELS.MEDIUM,
      code: null,
      message: '未知错误',
      originalMessage: '',
      stack: null,
      context: context,
      userAgent: this.getUserAgent(),
      url: this.getCurrentUrl()
    };

    if (error instanceof Error) {
      errorInfo.originalMessage = error.message;
      errorInfo.stack = error.stack;
      errorInfo.message = this.getErrorMessage(error.message) || error.message;
    } else if (typeof error === 'object') {
      errorInfo.code = error.code || error.statusCode;
      errorInfo.originalMessage = error.message || error.errMsg || '';
      errorInfo.message = this.getErrorMessage(error.code) || 
                         this.getErrorMessage(error.message) || 
                         errorInfo.originalMessage || '操作失败';
    } else if (typeof error === 'string') {
      errorInfo.originalMessage = error;
      errorInfo.message = this.getErrorMessage(error) || error;
    }

    // 根据错误信息确定类型和级别
    this.categorizeError(errorInfo);

    return errorInfo;
  }

  /**
   * 错误分类
   * @private
   * @param {Object} errorInfo - 错误信息
   */
  categorizeError(errorInfo) {
    const { code, originalMessage } = errorInfo;
    
    // 网络错误
    if (code === 'NETWORK_ERROR' || originalMessage.includes('网络') || 
        originalMessage.includes('timeout') || originalMessage.includes('连接')) {
      errorInfo.type = this.ERROR_TYPES.NETWORK;
      errorInfo.level = this.ERROR_LEVELS.MEDIUM;
    }
    // 认证错误
    else if (code === 401 || code === 403 || originalMessage.includes('登录') || 
             originalMessage.includes('权限') || originalMessage.includes('token')) {
      errorInfo.type = this.ERROR_TYPES.AUTH;
      errorInfo.level = this.ERROR_LEVELS.HIGH;
    }
    // 验证错误
    else if (code === 400 || originalMessage.includes('验证') || 
             originalMessage.includes('格式') || originalMessage.includes('必填')) {
      errorInfo.type = this.ERROR_TYPES.VALIDATION;
      errorInfo.level = this.ERROR_LEVELS.LOW;
    }
    // 服务器错误
    else if (code >= 500 || originalMessage.includes('服务器') || 
             originalMessage.includes('系统')) {
      errorInfo.type = this.ERROR_TYPES.SYSTEM;
      errorInfo.level = this.ERROR_LEVELS.HIGH;
    }
    // 业务错误
    else if (code >= 400 && code < 500) {
      errorInfo.type = this.ERROR_TYPES.BUSINESS;
      errorInfo.level = this.ERROR_LEVELS.MEDIUM;
    }
  }

  /**
   * 获取错误消息
   * @private
   * @param {string} key - 错误键
   * @returns {string} 错误消息
   */
  getErrorMessage(key) {
    if (!key) return null;
    
    // 直接匹配
    if (this.errorMessages[key]) {
      return this.errorMessages[key];
    }
    
    // 模糊匹配
    for (const [errorKey, message] of Object.entries(this.errorMessages)) {
      if (key.toLowerCase().includes(errorKey.toLowerCase()) || 
          errorKey.toLowerCase().includes(key.toLowerCase())) {
        return message;
      }
    }
    
    return null;
  }

  /**
   * 向用户显示错误
   * @private
   * @param {Object} errorInfo - 错误信息
   * @param {Object} options - 显示选项
   */
  showErrorToUser(errorInfo, options = {}) {
    const { type, level, message } = errorInfo;
    
    // 根据错误级别选择显示方式
    switch (level) {
      case this.ERROR_LEVELS.LOW:
        // 低级别错误，简单toast提示
        wx.showToast({
          title: message,
          icon: 'none',
          duration: 2000
        });
        break;
        
      case this.ERROR_LEVELS.MEDIUM:
        // 中级别错误，toast提示
        wx.showToast({
          title: message,
          icon: 'none',
          duration: 3000
        });
        break;
        
      case this.ERROR_LEVELS.HIGH:
      case this.ERROR_LEVELS.CRITICAL:
        // 高级别错误，模态框提示
        wx.showModal({
          title: '错误提示',
          content: message,
          showCancel: false,
          confirmText: '知道了'
        });
        break;
    }
  }

  /**
   * 记录错误
   * @private
   * @param {Object} errorInfo - 错误信息
   */
  recordError(errorInfo) {
    // 更新统计
    this.errorStats.total++;
    this.errorStats.byType[errorInfo.type] = (this.errorStats.byType[errorInfo.type] || 0) + 1;
    this.errorStats.byLevel[errorInfo.level] = (this.errorStats.byLevel[errorInfo.level] || 0) + 1;
    
    // 添加到最近错误列表
    this.errorStats.recent.unshift(errorInfo);
    
    // 限制记录数量
    if (this.errorStats.recent.length > this.config.maxErrorRecords) {
      this.errorStats.recent = this.errorStats.recent.slice(0, this.config.maxErrorRecords);
    }
    
    // 开发模式下打印详细错误信息
    if (this.config.showDetailedError) {
      // 错误信息已记录到统计中
    }
  }

  /**
   * 上报错误
   * @private
   * @param {Object} errorInfo - 错误信息
   */
  async reportError(errorInfo) {
    try {
      // 这里可以集成实际的错误上报服务

      // 示例：发送到服务器
      // await wx.request({
      //   url: this.config.reportUrl,
      //   method: 'POST',
      //   data: errorInfo
      // });

    } catch (reportError) {
      // 错误上报失败，静默处理
    }
  }

  /**
   * 生成错误ID
   * @private
   * @returns {string} 错误ID
   */
  generateErrorId() {
    return 'err_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取用户代理信息
   * @private
   * @returns {string} 用户代理
   */
  getUserAgent() {
    try {
      const deviceInfo = wx.getDeviceInfo();
      const appBaseInfo = wx.getAppBaseInfo();
      return `${deviceInfo.platform} ${deviceInfo.system} WeChat/${appBaseInfo.version}`;
    } catch (error) {
      return 'Unknown';
    }
  }

  /**
   * 获取当前页面URL
   * @private
   * @returns {string} 当前URL
   */
  getCurrentUrl() {
    try {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      return currentPage ? currentPage.route : 'Unknown';
    } catch (error) {
      return 'Unknown';
    }
  }

  /**
   * 获取错误统计
   * @returns {Object} 错误统计信息
   */
  getErrorStats() {
    return { ...this.errorStats };
  }

  /**
   * 清除错误记录
   */
  clearErrorRecords() {
    this.errorStats = {
      total: 0,
      byType: {},
      byLevel: {},
      recent: []
    };
  }

  /**
   * 更新配置
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }
}

// 创建全局实例
const errorManager = new ErrorManager();

module.exports = errorManager;
