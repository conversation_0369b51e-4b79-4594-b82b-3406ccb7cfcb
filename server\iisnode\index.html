<html>
<head>
    <title>iisnode logs</title>
    <style type="text/css">
        body
        {
            font-family: "Trebuchet MS" , Arial, Helvetica, sans-serif;
        }
        table
        {
            border-collapse: collapse;
        }
        td, th
        {
            border: 1px solid lightgray;
            padding: 3px 7px 2px 7px;
        }
        th
        {
            text-align: left;
            padding-top: 5px;
            padding-bottom: 4px;
            background-color: Gray;
            color: #ffffff;
        }
        td.stderr
        {
            color: Red;
        }
    </style>
</head>
<body>
    <table id="logFilesTable">
        <tr>
            <th>
                Computer name
            </th>
            <th>
                PID
            </th>
            <th>
                Type
            </th>
            <th>
                Created
            </th>
            <th>
                Link
            </th>
        </tr>
    </table>
    <p id="lastUpdated"></p>
    <script type="text/javascript">

        // this is replaced with actual data at runtime by code in interceptor.js
        var logFiles = [{"file":"LAPTOP-41PESEN1-14212-stderr-1754026542990.txt","computername":"LAPTOP-41PESEN1","pid":14212,"type":"stderr","created":1754026542990},{"file":"LAPTOP-41PESEN1-14212-stdout-1754026542988.txt","computername":"LAPTOP-41PESEN1","pid":14212,"type":"stdout","created":1754026542988},{"file":"LAPTOP-41PESEN1-19564-stderr-1754355144195.txt","computername":"LAPTOP-41PESEN1","pid":19564,"type":"stderr","created":1754355144195},{"file":"LAPTOP-41PESEN1-19564-stdout-1754355144193.txt","computername":"LAPTOP-41PESEN1","pid":19564,"type":"stdout","created":1754355144193},{"file":"LAPTOP-41PESEN1-2140-stderr-1754371922199.txt","computername":"LAPTOP-41PESEN1","pid":2140,"type":"stderr","created":1754371922199},{"file":"LAPTOP-41PESEN1-2140-stdout-1754371922197.txt","computername":"LAPTOP-41PESEN1","pid":2140,"type":"stdout","created":1754371922197},{"file":"LAPTOP-41PESEN1-25212-stderr-1754035224713.txt","computername":"LAPTOP-41PESEN1","pid":25212,"type":"stderr","created":1754035224713},{"file":"LAPTOP-41PESEN1-25212-stdout-1754035224711.txt","computername":"LAPTOP-41PESEN1","pid":25212,"type":"stdout","created":1754035224711},{"file":"LAPTOP-41PESEN1-27304-stderr-1754353666923.txt","computername":"LAPTOP-41PESEN1","pid":27304,"type":"stderr","created":1754353666923},{"file":"LAPTOP-41PESEN1-27304-stdout-1754353666921.txt","computername":"LAPTOP-41PESEN1","pid":27304,"type":"stdout","created":1754353666921},{"file":"LAPTOP-41PESEN1-31600-stderr-1754358123372.txt","computername":"LAPTOP-41PESEN1","pid":31600,"type":"stderr","created":1754358123372},{"file":"LAPTOP-41PESEN1-31600-stdout-1754358123370.txt","computername":"LAPTOP-41PESEN1","pid":31600,"type":"stdout","created":1754358123370},{"file":"LAPTOP-41PESEN1-33244-stderr-1754030793322.txt","computername":"LAPTOP-41PESEN1","pid":33244,"type":"stderr","created":1754030793322},{"file":"LAPTOP-41PESEN1-33244-stdout-1754030793320.txt","computername":"LAPTOP-41PESEN1","pid":33244,"type":"stdout","created":1754030793320},{"file":"LAPTOP-41PESEN1-34872-stderr-1754014143766.txt","computername":"LAPTOP-41PESEN1","pid":34872,"type":"stderr","created":1754014143766},{"file":"LAPTOP-41PESEN1-34872-stdout-1754014143763.txt","computername":"LAPTOP-41PESEN1","pid":34872,"type":"stdout","created":1754014143763},{"file":"LAPTOP-41PESEN1-36724-stderr-1754013197162.txt","computername":"LAPTOP-41PESEN1","pid":36724,"type":"stderr","created":1754013197162},{"file":"LAPTOP-41PESEN1-36724-stdout-1754013197159.txt","computername":"LAPTOP-41PESEN1","pid":36724,"type":"stdout","created":1754013197159},{"file":"LAPTOP-41PESEN1-38920-stderr-1754019257723.txt","computername":"LAPTOP-41PESEN1","pid":38920,"type":"stderr","created":1754019257723},{"file":"LAPTOP-41PESEN1-38920-stdout-1754019257721.txt","computername":"LAPTOP-41PESEN1","pid":38920,"type":"stdout","created":1754019257721},{"file":"LAPTOP-41PESEN1-8484-stderr-1754362368134.txt","computername":"LAPTOP-41PESEN1","pid":8484,"type":"stderr","created":1754362368134},{"file":"LAPTOP-41PESEN1-8484-stdout-1754362368132.txt","computername":"LAPTOP-41PESEN1","pid":8484,"type":"stdout","created":1754362368132}];
        var lastUpdated = 1754372284412;
        var date = new Date();

        date.setTime(lastUpdated);
        document.getElementById('lastUpdated').innerHTML = 'Index was last updated ' + date;

        logFiles.sort(function (a, b) {
            return b.created - a.created;
        });

        var logFilesTable = document.getElementById("logFilesTable");
        for (var i = 0; i < logFiles.length; i++) {
            var logFile = logFiles[i];
            date.setTime(logFile.created);
            var row = logFilesTable.insertRow(-1);
            var computerNameCell = row.insertCell(0);
            var pidCell = row.insertCell(1);
            var typeCell = row.insertCell(2);
            var dateCell = row.insertCell(3);
            var logCell = row.insertCell(4);
            computerNameCell.innerHTML = logFile.computername;
            pidCell.innerHTML = logFile.pid.toString();
            typeCell.innerHTML = logFile.type;
            typeCell.setAttribute('class', logFile.type);
            dateCell.innerHTML = date.toString();
            logCell.innerHTML = '<a href="' + logFile.file + '">log</a>';
        };

    </script>
</body>
</html>
