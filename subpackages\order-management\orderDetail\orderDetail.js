// pages/orderDetail/orderDetail.js
const requestOrder = require('../request/order')
const config = require('../../../config/common')
const utils = require('../../../utils/util');
const { userInfo } = require('../../../config/common');
Page({

    /**
     * 页面的初始数据
     */
    data: {
        CurrentBillNo: '',
        FromDetail: {},
        ShowDetail: {}
    },
    onNewOrderclick() {
        wx.navigateTo({
            url: '../orderNew/orderNew',
        })
    },
    onUpdateOrderclick() {
        if (this.data.ShowDetail.PermitState != 0) {
            wx.showModal({
                showCancel: false,
                title: '警告',
                content: '单据已审核，不允许修改',
            })
        }
        else {
            wx.navigateTo({
                url: '../orderUpdate/orderUpdate?BillNo=' + this.data.ShowDetail.BillNo
            })
        }
    },
    onDeleteOrderclick() {
        if (this.data.ShowDetail.PermitState == 0) {
            wx.showModal({
                title: '警告',
                content: '是否删除当前单据！！',
                complete: (res) => {
                    if (res.confirm) {
                        requestOrder.DeleteServApply({ ProgId: 'X_CarServApply', BillNo: this.data.CurrentBillNo }).then(res => {
                            if (res.data.status == 'suc') {
                                wx.showModal({
                                    showCancel: false,
                                    title: '删除成功',
                                    content: res.data.data,
                                    complete: (res) => {
                                        wx.navigateBack();
                                    }
                                })
                            }
                        }).catch(err => {
                            console.log(err);
                        })
                    }
                }
            })
        }
        else {
            wx.showToast({
                icon: "error",
                title: '已审核，禁删除！',
            })
        }
    },
    //车队长审批
    onCarLeaderclick() {
        this.searchRoleId(config.CDZRoleId).then(res => {
            if (this.data.ShowDetail.PermitState == 2) {
                wx.showModal({
                    title: '警告',
                    content: '该单据已审核，无法重复审批！',
                    showCancel: false,
                    complete: (res) => {
                    }
                })
            }
            else {
                wx.navigateTo({
                    url: '../orderCarLeaderApprove/orderCarLeaderApprove?BillNo=' + this.data.ShowDetail.BillNo,
                })
            }
        }).catch(err => {
            this.searchRoleId(config.BCRoleId).then(res => {
                if (this.data.ShowDetail.PermitState == 2) {
                    wx.showModal({
                        title: '警告',
                        content: '该单据已审核，无法重复审批！',
                        showCancel: false,
                        complete: (res) => {
                        }
                    })
                }
                else {
                    wx.navigateTo({
                        url: '../orderCarLeaderApprove/orderCarLeaderApprove?BillNo=' + this.data.ShowDetail.BillNo,
                    })
                }
            }).catch(err => {
                this.searchRoleId(config.JBCRoleId).then(res => {
                    if (this.data.ShowDetail.PermitState == 2) {
                        wx.showModal({
                            title: '警告',
                            content: '该单据已审核，无法重复审批！',
                            showCancel: false,
                            complete: (res) => {
                            }
                        })
                    }
                    else {
                        wx.navigateTo({
                            url: '../orderCarLeaderApprove/orderCarLeaderApprove?BillNo=' + this.data.ShowDetail.BillNo,
                        })
                    }
                }).catch(err => {
                    wx.showToast({
                        icon: 'error',
                        title: '车队长才能审批'
                    })
                })
            })
        })
    },
    //维修主管选择维修人
    onChosePersonclick() {
        this.searchRoleId(config.WXZGRoleId).then(res => {
            console.log("维修主管");
            wx.navigateTo({
                url: '../orderChoseRepair/orderChoseRepair?OrgId=' + this.data.ShowDetail.CompId
                    + '&BillNo=' + this.data.ShowDetail.BillNo
            })
        }).catch(err => {
            wx.showToast({
                icon: "error",
                title: '维修主管才能选择'
            })
        })
    },
    //搜索角色
    searchRoleId(roleId) {
        return new Promise((resolve, reject) => {
            utils.getStorage('userInfo').then(res => {
                let i = res.Roles.find(item => {
                    return item.RoleId === roleId;
                })
                if (i) { resolve(true) }
                else { reject(false) }
            }).catch(err => {
                reject(false)
            })
        })
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.data.CurrentBillNo = options.BillNo;
        utils.getStorage('userInfo').then(
            res => {
                requestOrder.GetServerApplyDetail({ ProgId: 'X_CarServApply', BillNo: this.data.CurrentBillNo, UserId: res.UserId }).then(res => {
                    if (res.data.status == 'suc') {
                        let tempDetail = {};
                        tempDetail.BillNo = res.data.data.BillNo;
                        tempDetail.BillDate = res.data.data.BillDate;
                        tempDetail.CompId = res.data.data.CompId;
                        tempDetail.CompName = res.data.data.CompName;
                        tempDetail.CarName = res.data.data.CarName;
                        tempDetail.TotalMileage = res.data.data.TotalMileage;
                        tempDetail.ServReason = res.data.data.ServReason;
                        tempDetail.ProposerName = res.data.data.ProposerName;
                        tempDetail.ProposeDeptName = res.data.data.ProposeDeptName;
                        tempDetail.FixState = res.data.data.FixState;
                        tempDetail.PermitterName = res.data.data.PermitterName;
                        if (tempDetail.FixState == 0) { tempDetail.FixStateName = "申报中"; }
                        if (tempDetail.FixState == 1) { tempDetail.FixStateName = "待修中"; }
                        if (tempDetail.FixState == 2) { tempDetail.FixStateName = "维修中"; }
                        if (tempDetail.FixState == 3) { tempDetail.FixStateName = "维修完成"; }
                        if (tempDetail.FixState == 4) { tempDetail.FixStateName = "已验收"; }
                        tempDetail.PermitState = res.data.data.PermitState;
                        if (res.data.data.PermitState == 0) { tempDetail.PermitStateName = "未审核"; }
                        if (res.data.data.PermitState == 1) { tempDetail.PermitStateName = "审核中"; }
                        if (res.data.data.PermitState == 2) { tempDetail.PermitStateName = "已审核"; }
                        tempDetail.FixPersonName = res.data.data.FixPersonName;
                        tempDetail.CarMark = res.data.data.CarMark;
                        tempDetail.IsApprove = res.data.data.IsApprove;
                        if (res.data.data.IsApprove == 0) { tempDetail.IsApproveName = "未审核"; }
                        if (res.data.data.IsApprove == 1) { tempDetail.IsApproveName = "同意"; }
                        if (res.data.data.IsApprove == 2) { tempDetail.IsApproveName = "否决"; }
                        tempDetail.ApprovalOpinions = res.data.data.ApprovalOpinions;
                        tempDetail.repairProjectDetail = res.data.data.repairProjectDetail;
                        tempDetail.WxOperaPersonId = res.data.data.WxOperaPersonId;
                        tempDetail.WxOperaPersonName = res.data.data.WxOperaPersonName;
                        tempDetail.OperaPhone = res.data.data.OperaPhone;
                        this.setData({
                            ShowDetail: tempDetail
                        })
                        console.log('showDetail', this.data.ShowDetail)
                    }
                    else {
                        console.log('找不到单据');
                    }
                }).catch(err => {
                    console.log('detail-err', err);
                })
            }
        )


    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})