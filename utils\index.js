// 工具函数统一入口

const RequestUtil = require('./request');
const Validator = require('./validator');
const Formatter = require('./formatter');
const StorageUtil = require('./storage');
const CacheManager = require('./cache-manager');
const LoadingManager = require('./loading-manager');
const ErrorManager = require('./error-manager');


// 业务状态映射
const STATUS_MAPS = {
  COMMON_STATUS: {
    0: '禁用',
    1: '启用'
  },
  TASK_STATUS: {
    pending: '待处理',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  },
  SUPPLY_STATUS: {
    0: '待供',
    1: '正供',
    2: '供毕',
    3: '作废',
    // 兼容旧版本
    pending: '待供',
    supplying: '正供',
    completed: '供毕',
    cancelled: '作废'
  },
  FEEDBACK_STATUS: {
    pending: '待反馈',
    in_progress: '反馈中',
    completed: '已完成',
    failed: '反馈失败'
  }
};

/**
 * 常用的文件类型配置
 */
const FILE_TYPES = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  VIDEO: ['mp4', 'avi', 'mov', 'wmv', 'flv'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  AUDIO: ['mp3', 'wav', 'aac', 'flac']
};

/**
 * 常用的文件大小限制（字节）
 */
const FILE_SIZE_LIMITS = {
  IMAGE: 5 * 1024 * 1024,      // 5MB
  VIDEO: 100 * 1024 * 1024,    // 100MB
  DOCUMENT: 10 * 1024 * 1024,  // 10MB
  AUDIO: 20 * 1024 * 1024      // 20MB
};

/**
 * 通用工具函数
 */
const CommonUtils = {
  /**
   * 防抖函数
   * @param {Function} func - 要防抖的函数
   * @param {number} delay - 延迟时间（毫秒）
   * @returns {Function} 防抖后的函数
   */
  debounce(func, delay) {
    let timeoutId;
    return function () {
      const args = Array.prototype.slice.call(arguments);
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  },

  /**
   * 节流函数
   * @param {Function} func - 要节流的函数
   * @param {number} delay - 延迟时间（毫秒）
   * @returns {Function} 节流后的函数
   */
  throttle(func, delay) {
    let lastCall = 0;
    return function () {
      const args = Array.prototype.slice.call(arguments);
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        return func.apply(this, args);
      }
    };
  },

  /**
   * 深拷贝对象
   * @param {*} obj - 要拷贝的对象
   * @returns {*} 拷贝后的对象
   */
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }
    
    if (obj instanceof Date) {
      return new Date(obj.getTime());
    }
    
    if (obj instanceof Array) {
      return obj.map(item => this.deepClone(item));
    }
    
    if (typeof obj === 'object') {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = this.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  },

  /**
   * 生成唯一ID
   * @param {number} length - ID长度，默认8位
   * @returns {string} 唯一ID
   */
  generateId(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * 检查对象是否为空
   * @param {*} obj - 要检查的对象
   * @returns {boolean} 是否为空
   */
  isEmpty(obj) {
    if (obj === null || obj === undefined) return true;
    if (typeof obj === 'string') return obj.trim().length === 0;
    if (Array.isArray(obj)) return obj.length === 0;
    if (typeof obj === 'object') return Object.keys(obj).length === 0;
    return false;
  },

  /**
   * 安全的JSON解析
   * @param {string} jsonString - JSON字符串
   * @param {*} defaultValue - 解析失败时的默认值
   * @returns {*} 解析结果
   */
  safeJsonParse(jsonString, defaultValue = null) {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('JSON解析失败:', error);
      return defaultValue;
    }
  },

  /**
   * 安全的JSON字符串化
   * @param {*} obj - 要字符串化的对象
   * @param {string} defaultValue - 失败时的默认值
   * @returns {string} JSON字符串
   */
  safeJsonStringify(obj, defaultValue = '{}') {
    try {
      return JSON.stringify(obj);
    } catch (error) {
      console.error('JSON字符串化失败:', error);
      return defaultValue;
    }
  },

  /**
   * 获取文件扩展名
   * @param {string} fileName - 文件名
   * @returns {string} 扩展名（小写）
   */
  getFileExtension(fileName) {
    if (!fileName || typeof fileName !== 'string') return '';
    const lastDotIndex = fileName.lastIndexOf('.');
    return lastDotIndex !== -1 ? fileName.substring(lastDotIndex + 1).toLowerCase() : '';
  },

  /**
   * 检查是否为图片文件
   * @param {string} fileName - 文件名
   * @returns {boolean} 是否为图片
   */
  isImageFile(fileName) {
    const ext = this.getFileExtension(fileName);
    return FILE_TYPES.IMAGE.includes(ext);
  },

  /**
   * 检查是否为视频文件
   * @param {string} fileName - 文件名
   * @returns {boolean} 是否为视频
   */
  isVideoFile(fileName) {
    const ext = this.getFileExtension(fileName);
    return FILE_TYPES.VIDEO.includes(ext);
  },

  /**
   * 获取随机颜色
   * @returns {string} 十六进制颜色值
   */
  getRandomColor() {
    return '#' + Math.floor(Math.random() * 16777215).toString(16);
  },

  /**
   * 延迟执行
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise对象
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
};

/**
 * 小程序专用工具函数
 */
const WxUtils = {
  /**
   * 显示成功提示
   * @param {string} title - 提示文本
   * @param {number} duration - 显示时长
   */
  showSuccess(title, duration = 2000) {
    wx.showToast({
      title,
      icon: 'success',
      duration
    });
  },

  /**
   * 显示错误提示
   * @param {string} title - 提示文本
   * @param {number} duration - 显示时长
   */
  showError(title, duration = 2000) {
    wx.showToast({
      title,
      icon: 'none',
      duration
    });
  },

  /**
   * 显示加载提示
   * @param {string} title - 提示文本
   */
  showLoading(title = '加载中...') {
    wx.showLoading({
      title,
      mask: true
    });
  },

  /**
   * 隐藏加载提示
   */
  hideLoading() {
    wx.hideLoading();
  },

  /**
   * 显示确认对话框
   * @param {string} content - 对话框内容
   * @param {string} title - 对话框标题
   * @returns {Promise<boolean>} 用户是否确认
   */
  showConfirm(content, title = '提示') {
    return new Promise((resolve) => {
      wx.showModal({
        title,
        content,
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  },

  /**
   * 复制文本到剪贴板
   * @param {string} text - 要复制的文本
   * @returns {Promise<boolean>} 是否复制成功
   */
  copyText(text) {
    return new Promise((resolve) => {
      wx.setClipboardData({
        data: text,
        success: () => {
          this.showSuccess('复制成功');
          resolve(true);
        },
        fail: () => {
          this.showError('复制失败');
          resolve(false);
        }
      });
    });
  }
};

module.exports = {
  RequestUtil,
  Validator,
  Formatter,
  StorageUtil,
  CacheManager,
  LoadingManager,
  ErrorManager,

  CommonUtils,
  WxUtils,
  STATUS_MAPS,
  FILE_TYPES,
  FILE_SIZE_LIMITS
};
