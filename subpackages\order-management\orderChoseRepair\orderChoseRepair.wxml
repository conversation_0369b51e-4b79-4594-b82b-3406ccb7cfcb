<!--pages/update_userinfo/update_userinfo.wxml-->
<!-- 弹窗功能（用于确认个人信息） -->
<!-- 遮罩层 -->
<view>
    <form bindsubmit="formSubmit">
        <view class='cover'>
            <!-- 可在此按需求自定义遮罩 -->
            <view style="position: relative;">
                <view class='cover_child'>
                    <!-- 信息输入界面 -->
                    <text class="child-title">维修主管选择维修人</text>
                    <radio-group bindchange="radioChange" class="radio-group">
                        <view class="radioinfo" wx:for="{{FixPersonList}}" wx:key="index">
                            <view class="">
                                <radio value="{{item.FixPersonId}}" />
                            </view>
                            <view class="fixName">{{item.FixPersonName}}</view>
                        </view>
                    </radio-group>
                </view>
                <!-- 取消、确定按钮 -->
                <view class='btn-group'>
                    <button type="default" size="mini" catchtap="hideCover">取消</button>
                    <button type="primary" size="mini" form-type='submit' bindtap="openNews">提交</button>
                </view>
            </view>
        </view>
    </form>
</view>