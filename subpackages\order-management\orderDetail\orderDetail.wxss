/* pages/orderDetail/orderDetail.wxss *//* components/CarOrder/CarOrderCard/CarOrderCard.wxss */
page{
    width: 100%;
    height: 100%;
    background-color:rgb(223, 223, 223);
}
.CarCard{
    margin: 0;
    font-size:large;
}
.inTitle{
    margin-top: 10rpx;
    margin-left: 12rpx;
}
.Info{
    margin-left: 10rpx;
    margin-right: 10rpx;
    padding: 10rpx;
    padding-top: 20rpx;
    border: 2px double grey;
    background-color: white;
}
.Info view{
    margin-bottom: 10rpx;
}
.buttonArea
{
    margin-top: 10rpx;
    display: flex;
    justify-content: space-around;
}
.titleinput
{
    display: flex;
    margin-bottom: 20rpx;
}
.titleinput view
{
    height: 50rpx;
    line-height: 50rpx;
}
.titleinput input
{
    margin-bottom: 10rpx;
    height: 50rpx;
    line-height: 50rpx;
    border: 1px solid black;
}
.titleinputReason{
    display: flex;
}