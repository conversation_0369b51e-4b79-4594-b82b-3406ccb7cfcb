var app = getApp();
var ImageHelper = require('../utils/image-helper');
var TiandituGeocoding = require('../utils/tianditu-geocoding.js');
var LocationPermissionManager = require('../../../utils/location-permission-manager.js');



Page({
  data: {
    currentTask: null,
    currentCompany: null,
    userInfo: null, // 当前用户信息

    showCategorySelector: false,

    feedbackForm: {
      feedback_time: "",
      notes: "",
      category: "", // 反馈类别
      longitude: null, // 经度
      latitude: null, // 纬度
      location_desc: "", // 位置描述
      location_status: "unavailable" // 位置状态：authorized, denied, unavailable
    },
    // 地图标记点
    mapMarkers: [],
    // 反馈类别选项
    feedbackCategories: [
      "反馈类别1",
      "反馈类别2",
      "反馈类别3",
      "反馈类别4",
      "反馈类别5",
      "反馈类别6",
      "反馈类别7",
      "反馈类别8",
      "反馈类别9",
      "反馈类别10",
      "反馈类别11",
    ],
    dateTimeRange: [[], [], [], [], []],
    dateTimeValue: [0, 0, 0, 0, 0],
    images: [],
    videos: [],
    audios: [],
    recording: false,
    recordTime: 0,
    recordTimer: null,
    submitting: false,
    // 音频播放相关状态
    currentPlayingIndex: -1, // 当前播放的音频索引
    audioContext: null, // 音频上下文

    playingStates: [], // 每个音频的播放状态
    // 视频时长控制设置
    videoDurationSettings: null,

    // 页面状态管理
    isUploading: false, // 是否正在上传文件
    uploadProgress: 0, // 上传进度
    hasUnsavedData: false, // 是否有未保存的数据
    submitSuccess: false, // 提交是否成功

    // 超时功能相关
    timeoutEnabled: false, // 是否启用超时功能
    timeoutDuration: 2 * 60, // 超时时长（秒），默认2分钟
    remainingTime: 0, // 剩余时间（秒）
    timeoutTimer: null, // 超时定时器
    timeoutDisplay: '', // 显示的倒计时文本
    timeoutWarning: false, // 是否显示警告状态（最后30秒）
    isTimeout: false, // 是否已超时
  },

  onLoad(options) {
    this.checkLogin();
    this.initDateTime();

    // 立即设置默认视频时长设置，避免显示"加载中"
    const videoDurationManager = require('../utils/video-duration-manager');
    this.setData({
      videoDurationSettings: videoDurationManager.getSettings(),
    });

    // 异步加载实际设置
    this.loadVideoDurationSettings();

    // 获取位置信息（权限已在登录时确认）
    this.requestLocation();

    if (options.taskId) {
      this.loadTask(options.taskId);
    } else {
      // 没有指定任务单或工程时，提示用户需要先选择任务单
      wx.showToast({
        title: "请先选择任务单",
        icon: "none",
      });
    }

    // 监听页面数据变化，标记为有未保存数据
    this.setupDataChangeListeners();

    // 启动超时功能
    this.startTimeout();
  },

  onShow() {
    // 页面显示时检查是否有未完成的上传
    if (this.data.isUploading) {
      wx.showLoading({
        title: '上传进行中...',
        mask: true
      });
    }

    // 恢复页面保护状态
    if (this.data.isUploading || this.data.hasUnsavedData) {
      this.managePageProtection(true);
    }

    // 恢复超时计时器（如果之前被暂停）
    if (this.data.timeoutEnabled && !this.data.timeoutTimer && !this.data.isTimeout) {
      this.resumeTimeout();
    }
  },

  onHide() {
    // 页面隐藏时保存当前状态到本地存储
    if (this.data.isUploading || this.data.hasUnsavedData) {
      console.log('页面隐藏，保存当前状态');
      this.savePageState();
    }

    // 暂停超时计时器，避免在后台继续计时
    if (this.data.timeoutEnabled && !this.data.isTimeout) {
      this.pauseTimeout();
    }
  },

  onReady() {
    // 页面首次渲染完成时恢复状态
    this.restorePageState();
  },

  onUnload() {
    // 如果正在上传且未成功，警告用户
    if (this.data.isUploading && !this.data.submitSuccess) {
      console.warn('页面卸载时仍在上传，可能导致数据丢失');
      // 尝试取消正在进行的上传任务
      this.cancelUploadTasks();
    }

    // 清理录音定时器
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }

    // 清理自动保存定时器
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
    }

    // 清理超时定时器
    if (this.data.timeoutTimer) {
      clearInterval(this.data.timeoutTimer);
    }

    // 清理音频播放
    this.stopAllAudio();

    // 确保移除页面保护
    this.managePageProtection(false);

    // 页面卸载时不需要清理文件，因为现在文件只在提交时上传
  },

  // 设置数据变化监听器
  setupDataChangeListeners() {
    // 监听表单数据变化
    const originalSetData = this.setData;
    this.setData = (data, callback) => {
      // 检查是否有关键数据变化
      if (data && (
        data['feedbackForm.notes'] ||
        data['feedbackForm.category'] ||
        data.images ||
        data.videos ||
        data.audios
      )) {
        // 标记有未保存数据（除非正在提交）
        if (!this.data.submitting && !this.data.submitSuccess) {
          data.hasUnsavedData = true;
        }
      }

      originalSetData.call(this, data, callback);
    };
  },

  // 取消上传任务
  cancelUploadTasks() {
    // 这里可以添加取消上传任务的逻辑
    // 由于微信小程序的uploadFile任务无法直接取消，
    // 我们只能标记状态并在回调中忽略结果
    this.setData({
      isUploading: false,
      uploadProgress: 0
    });
  },

  // 保存页面状态到本地存储
  savePageState() {
    try {
      const pageState = {
        feedbackForm: this.data.feedbackForm,
        images: this.data.images,
        videos: this.data.videos,
        audios: this.data.audios,
        currentTask: this.data.currentTask,

        isUploading: this.data.isUploading,
        uploadProgress: this.data.uploadProgress,
        hasUnsavedData: this.data.hasUnsavedData,
        submitSuccess: this.data.submitSuccess,
        timestamp: Date.now()
      };

      wx.setStorageSync('feedback_page_state', pageState);
      console.log('页面状态已保存');
    } catch (error) {
      console.error('保存页面状态失败:', error);
    }
  },

  // 恢复页面状态
  restorePageState() {
    try {
      const pageState = wx.getStorageSync('feedback_page_state');
      if (pageState && pageState.timestamp) {
        // 检查状态是否过期（30分钟）
        const now = Date.now();
        const stateAge = now - pageState.timestamp;
        const maxAge = 30 * 60 * 1000; // 30分钟

        if (stateAge < maxAge && (pageState.isUploading || pageState.hasUnsavedData)) {
          console.log('恢复页面状态');

          // 恢复关键数据
          this.setData({
            feedbackForm: pageState.feedbackForm || this.data.feedbackForm,
            images: pageState.images || [],
            videos: pageState.videos || [],
            audios: pageState.audios || [],
            currentTask: pageState.currentTask || this.data.currentTask,

            isUploading: pageState.isUploading || false,
            uploadProgress: pageState.uploadProgress || 0,
            hasUnsavedData: pageState.hasUnsavedData || false,
            submitSuccess: pageState.submitSuccess || false
          });

          // 如果正在上传，恢复页面保护
          if (pageState.isUploading) {
            this.managePageProtection(true);
            wx.showLoading({
              title: '恢复上传状态...',
              mask: true
            });

            // 3秒后隐藏loading，让用户决定是否继续
            setTimeout(() => {
              wx.hideLoading();
              wx.showModal({
                title: '检测到未完成的上传',
                content: '检测到之前有未完成的文件上传，是否继续？',
                confirmText: '继续上传',
                cancelText: '重新开始',
                success: (res) => {
                  if (res.confirm) {
                    // 用户选择继续，保持当前状态
                    this.managePageProtection(true);
                  } else {
                    // 用户选择重新开始，清除状态
                    this.clearPageState();
                    this.setData({
                      isUploading: false,
                      uploadProgress: 0,
                      hasUnsavedData: false,
                      submitSuccess: false
                    });
                    this.managePageProtection(false);
                  }
                }
              });
            }, 3000);
          }
        } else {
          // 状态过期或无需恢复，清除存储
          this.clearPageState();
        }
      }
    } catch (error) {
      console.error('恢复页面状态失败:', error);
      this.clearPageState();
    }
  },

  // 清除页面状态
  clearPageState() {
    try {
      wx.removeStorageSync('feedback_page_state');
      console.log('页面状态已清除');
    } catch (error) {
      console.error('清除页面状态失败:', error);
    }
  },

  // 启动超时功能
  startTimeout() {
    // 检查是否为调试模式（可以通过页面参数传入）
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage.options || {};

    // 如果传入了debug参数，使用较短的超时时间进行测试
    let timeoutDuration = this.data.timeoutDuration;
    if (options.debug === 'true') {
      timeoutDuration = 30; // 调试模式下30秒超时
      console.log('调试模式：超时时间设置为30秒');
    }

    this.setData({
      timeoutEnabled: true,
      remainingTime: timeoutDuration,
      timeoutDuration: timeoutDuration,
      isTimeout: false
    });

    this.updateTimeoutDisplay();

    // 启动定时器，每秒更新一次
    const timer = setInterval(() => {
      const newRemainingTime = this.data.remainingTime - 1;

      if (newRemainingTime <= 0) {
        // 时间到了，触发超时
        this.handleTimeout();
        return;
      }

      this.setData({
        remainingTime: newRemainingTime,
        timeoutWarning: newRemainingTime <= 30 // 最后30秒显示警告
      });

      this.updateTimeoutDisplay();
    }, 1000);

    this.setData({ timeoutTimer: timer });
  },

  // 更新超时显示文本
  updateTimeoutDisplay() {
    const minutes = Math.floor(this.data.remainingTime / 60);
    const seconds = this.data.remainingTime % 60;
    const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    this.setData({
      timeoutDisplay: display
    });
  },

  // 处理超时
  handleTimeout() {
    // 清理定时器
    if (this.data.timeoutTimer) {
      clearInterval(this.data.timeoutTimer);
    }

    this.setData({
      isTimeout: true,
      timeoutEnabled: false,
      timeoutDisplay: '已超时'
    });

    // 显示超时提示
    wx.showModal({
      title: '填写超时',
      content: '现场信息反馈填写已超时，请重新选择任务单',
      showCancel: false,
      confirmText: '确定',
      success: () => {
        this.returnToPreviousPage();
      }
    });
  },

  // 暂停超时计时器
  pauseTimeout() {
    if (this.data.timeoutTimer) {
      clearInterval(this.data.timeoutTimer);
      this.setData({ timeoutTimer: null });
    }
  },

  // 恢复超时计时器
  resumeTimeout() {
    if (this.data.timeoutEnabled && !this.data.isTimeout && this.data.remainingTime > 0) {
      const timer = setInterval(() => {
        const newRemainingTime = this.data.remainingTime - 1;

        if (newRemainingTime <= 0) {
          // 时间到了，触发超时
          this.handleTimeout();
          return;
        }

        this.setData({
          remainingTime: newRemainingTime,
          timeoutWarning: newRemainingTime <= 30 // 最后30秒显示警告
        });

        this.updateTimeoutDisplay();
      }, 1000);

      this.setData({ timeoutTimer: timer });
    }
  },

  // 返回上一个页面
  returnToPreviousPage() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack({
        success: () => {
          console.log('超时后返回上一页成功');
        },
        fail: (error) => {
          console.error('超时后返回上一页失败:', error);
          // 返回失败，跳转到任务单列表
          this.navigateToTaskList();
        }
      });
    } else {
      // 没有上一页，跳转到任务单列表
      this.navigateToTaskList();
    }
  },

  // 管理页面保护状态
  managePageProtection(enable) {
    if (enable) {
      // 监听页面返回事件
      wx.onBackPress && wx.onBackPress(() => {
        // 如果已经提交成功，允许正常返回
        if (this.data.submitSuccess) {
          this.clearPageState(); // 清除状态
          return false;
        }

        if (this.data.isUploading) {
          wx.showModal({
            title: '提示',
            content: '正在上传文件，确定要离开吗？离开可能导致上传失败。',
            confirmText: '继续上传',
            cancelText: '强制离开',
            success: (res) => {
              if (!res.confirm) {
                this.cancelUploadTasks();
                this.clearPageState();
                wx.navigateBack();
              }
            }
          });
          return true;
        }
        return false;
      });

      // 监听小程序切换到后台
      wx.onAppHide && wx.onAppHide(() => {
        if (this.data.isUploading && !this.data.submitSuccess) {
          console.log('应用切换到后台，保存上传状态');
          this.savePageState();
        }
      });
    } else {
      // 移除事件监听器
      wx.offBackPress && wx.offBackPress();
      wx.offAppHide && wx.offAppHide();
    }
  },

  // 检查登录状态
  checkLogin() {
    const userInfo = wx.getStorageSync("userInfo");
    const currentCompany = wx.getStorageSync("currentCompany");

    if (!userInfo) {
      wx.redirectTo({
        url: "/pages/login/login",
      });
      return;
    }

    if (!currentCompany) {
      wx.showToast({
        title: "请先选择公司",
        icon: "none",
      });
      setTimeout(() => {
        wx.switchTab({
          url: "/pages/index/index",
        });
      }, 2000);
      return;
    }

    // 设置用户信息到data中
    this.setData({
      userInfo,
      currentCompany,
    });
  },

  // 初始化日期时间选择器
  initDateTime() {
    const now = new Date();
    const years = [];
    const months = [];
    const days = [];
    const hours = [];
    const minutes = [];

    // 生成年份 当前年份前后5年
    for (let i = now.getFullYear() - 5; i <= now.getFullYear() + 5; i++) {
      years.push(i + "年");
    }

    // 生成月份
    for (let i = 1; i <= 12; i++) {
      months.push(i + "月");
    }

    // 生成日期
    for (let i = 1; i <= 31; i++) {
      days.push(i + "日");
    }

    // 生成小时
    for (let i = 0; i < 24; i++) {
      hours.push(String(i).padStart(2, "0") + "时");
    }

    // 生成分钟
    for (let i = 0; i < 60; i++) {
      minutes.push(String(i).padStart(2, "0") + "分");
    }

    // 计算当前时间在数组中的正确索引
    const currentYearIndex = 5; // 当前年份在数组中的索引（前后5年，所以当前年份在第6个位置，索引为5）
    const currentMonthIndex = now.getMonth(); // 月份索引（0-11）
    const currentDayIndex = now.getDate() - 1; // 日期索引（0-30，因为数组从1日开始）
    const currentHourIndex = now.getHours(); // 小时索引（0-23）
    const currentMinuteIndex = now.getMinutes(); // 分钟索引（0-59）

    this.setData({
      dateTimeRange: [years, months, days, hours, minutes],
      dateTimeValue: [
        currentYearIndex,
        currentMonthIndex,
        currentDayIndex,
        currentHourIndex,
        currentMinuteIndex,
      ],
    });

    this.updateFeedbackTime();
  },

  // 统一的位置获取方法
  async getLocationWithOptions(options = {}) {
    const that = this;
    const {
      isHighAccuracy = false,
      highAccuracyExpireTime = 3000,
      useCache = true,
      showProgress = false,
      showSuccessModal = false
    } = options;

    try {
      // 获取位置信息
      const locationOptions = isHighAccuracy ? {
        isHighAccuracy: true,
        highAccuracyExpireTime: highAccuracyExpireTime
      } : {};

      const locationResult = await LocationPermissionManager.getCurrentLocation(locationOptions);

      if (locationResult.success) {
        // 位置获取成功
        that.setData({
          'feedbackForm.longitude': locationResult.data.longitude,
          'feedbackForm.latitude': locationResult.data.latitude,
          'feedbackForm.location_status': 'authorized'
        });

        // 更新地图标记点
        that.updateMapMarkers(locationResult.data.latitude, locationResult.data.longitude);

        // 使用天地图地理编码获取地址
        const result = await TiandituGeocoding.smartGeocode(
          locationResult.data.latitude,
          locationResult.data.longitude,
          {
            useCache: useCache,
            showProgress: showProgress
          }
        );

        if (result.success) {
          that.setData({
            'feedbackForm.location_desc': result.address
          });

          if (showSuccessModal) {
            wx.showModal({
              title: '位置更新完成',
              content: '定位成功！\n\n' + result.address,
              showCancel: false,
              confirmText: '好的'
            });
          } else {
            wx.showToast({
              title: '位置获取成功',
              icon: 'success',
              duration: 2000
            });
          }
        } else {
          // 降级方案：显示位置获取成功但地址解析失败的提示
          const fallbackDesc = '位置获取成功，地址解析失败';
          that.setData({
            'feedbackForm.location_desc': fallbackDesc
          });

          if (showSuccessModal) {
            wx.showModal({
              title: '位置更新完成',
              content: '⚠️ 天地图API不可用，无法获取详细地址',
              showCancel: false,
              confirmText: '好的'
            });
          } else {
            wx.showToast({
              title: '位置解析失败',
              icon: 'none',
              duration: 2000
            });
          }
        }
      } else {
        throw new Error('位置获取失败');
      }
    } catch (error) {
      console.error('获取位置失败:', error);
      this.handleLocationError(error);
      throw error; // 重新抛出错误以便调用者处理
    }
  },

  // 统一的位置错误处理
  handleLocationError(error) {
    let errorMsg = '获取位置失败';
    if (error.errorCode === 'PERMISSION_DENIED') {
      errorMsg = '位置权限被拒绝，请在设置中开启位置权限';
      LocationPermissionManager.showSettingsGuide();  
    } else if (error.errorMsg) {
      errorMsg = error.errorMsg;
    }

    this.setData({
      'feedbackForm.location_status': 'denied',
      'feedbackForm.location_desc': errorMsg
    });

    if (error.errorCode !== 'PERMISSION_DENIED') {
      wx.showToast({
        title: errorMsg,
        icon: 'none'
      });
    }
  },

  // 统一的网络错误处理
  handleNetworkError(error, context = '') {
    let errorMessage = "网络错误";

    if (error.message) {
      errorMessage = error.message;
    } else if (error.errMsg) {
      if (error.errMsg.includes("timeout")) {
        errorMessage = "请求超时，请检查网络连接";
      } else if (error.errMsg.includes("fail")) {
        errorMessage = "网络连接失败，请重试";
      } else if (error.errMsg.includes("ECONNRESET")) {
        errorMessage = "网络连接中断，请检查网络后重试";
      } else if (error.errMsg.includes("ENOTFOUND")) {
        errorMessage = "无法连接到服务器，请检查网络设置";
      } else {
        errorMessage = error.errMsg;
      }
    }

    if (context) {
      console.error(`${context}失败:`, error);
    }

    return errorMessage;
  },

  // 统一的上传错误处理
  handleUploadError(error, fileType, index) {
    let errorMsg = this.handleNetworkError(error);

    // 针对上传的特殊错误处理
    if (errorMsg.includes("timeout")) {
      errorMsg = fileType === 'video' ?
        "上传超时，文件可能过大，建议压缩后重试" :
        "上传超时，请检查网络或稍后重试";
    }

    return `${fileType} ${index + 1}: ${errorMsg}`;
  },



  // 获取位置信息（权限已确认）
  async requestLocation() {
    try {
      await this.getLocationWithOptions();
    } catch (error) {
      // 错误已在getLocationWithOptions中处理
    }
  },



  // 更新地图标记点
  updateMapMarkers(latitude, longitude) {
    const markers = [{
      id: 1,
      latitude: latitude,
      longitude: longitude,
      width: 30,
      height: 30,
      title: '反馈位置',
      // 移除callout配置以避免显示空白气泡
      // 使用默认标记图标（确保兼容性）
      iconPath: '',
      anchor: {
        x: 0.5,
        y: 1
      }
    }];

    this.setData({
      mapMarkers: markers
    });
  },

  // 地图点击事件
  onMapTap() {
    const { latitude, longitude, location_desc } = this.data.feedbackForm;

    if (latitude && longitude) {
      // 显示详细位置信息
      wx.showModal({
        title: '反馈位置信息',
        content: `位置描述：${location_desc}`,
        confirmText: '确定',
        showCancel: false
      });
    } else {
      wx.showToast({
        title: '位置信息不可用',
        icon: 'none',
        duration: 1500
      });
    }
  },

  // 重新获取位置
  async refreshAccurateLocation() {
    wx.showLoading({
      title: '获取位置信息...'
    });

    try {
      await this.getLocationWithOptions({
        isHighAccuracy: true,
        highAccuracyExpireTime: 3000,
        useCache: false,
        showProgress: true,
        showSuccessModal: true
      });
    } catch (error) {
      console.error('刷新位置失败:', error);
      this.handleLocationError(error);
    } finally {
      wx.hideLoading();
    }
  },

  // 更新反馈时间
  updateFeedbackTime() {
    const { dateTimeRange, dateTimeValue } = this.data;
    const year = parseInt(dateTimeRange[0][dateTimeValue[0]]);
    const month = dateTimeValue[1] + 1;
    const day = dateTimeValue[2] + 1;
    const hour = dateTimeValue[3];
    const minute = dateTimeValue[4];

    // 使用统一的时间格式化方法
    const Formatter = require('../utils/formatter');
    const timeStr = Formatter.createTimeString(year, month, day, hour, minute);

    this.setData({
      "feedbackForm.feedback_time": timeStr,
    });
  },

  // 加载任务单
  async loadTask(taskId) {
    try {
      const app = getApp();
      const res = await app.request({
        url: "/api/tasks/" + taskId,
        method: "GET",
      });

      if (res.data.success) {
        const task = Object.assign({}, res.data.data.task, {
          scheduled_time_text: this.formatDateTime(res.data.data.task.scheduled_time)
        });
        this.setData({
          currentTask: task,
        });
      } else {
        wx.showToast({
          title: res.data.message || "加载任务单失败",
          icon: "none",
        });
      }
    } catch (error) {
      wx.showToast({
        title: "网络错误",
        icon: "none",
      });
    }
  },





  // 格式化日期时间
  formatDateTime(dateTimeStr) {
    const Formatter = require('../utils/formatter');
    return Formatter.formatDateTime(dateTimeStr, 'YYYY-MM-DD HH:mm');
  },

  // 表单输入处理
  onDateTimeChange(e) {
    this.setData({
      dateTimeValue: e.detail.value,
    });
    this.updateFeedbackTime();
  },

  // 输入备注
  onNotesInput(e) {
    this.setData({
      "feedbackForm.notes": e.detail.value,
      hasUnsavedData: true // 标记有未保存数据
    });

    // 延迟保存状态，避免频繁保存
    this.debounceAutoSave();
  },

  // 防抖自动保存
  debounceAutoSave() {
    if (this.autoSaveTimer) {
      clearTimeout(this.autoSaveTimer);
    }

    this.autoSaveTimer = setTimeout(() => {
      if (this.data.hasUnsavedData && !this.data.isUploading) {
        this.savePageState();
      }
    }, 2000); // 2秒后自动保存
  },

  // 显示/隐藏反馈类别选择器
  onShowCategorySelector() {
    const currentState = this.data.showCategorySelector;
    this.setData({ showCategorySelector: !currentState });
  },

  // 关闭反馈类别选择器
  onCloseCategorySelector() {
    this.setData({ showCategorySelector: false });
  },

  // 选择反馈类别
  onSelectCategory(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      "feedbackForm.category": category,
      showCategorySelector: false,
      hasUnsavedData: true // 标记有未保存数据
    });

    // 自动保存状态
    this.debounceAutoSave();
  },

  // 打开相机拍摄页面
  onOpenCamera(e) {
    // 防止在上传过程中打开相机
    if (this.data.isUploading) {
      wx.showModal({
        title: '提示',
        content: '正在上传文件，请等待上传完成后再拍摄',
        showCancel: false
      });
      return;
    }

    const mode = e.currentTarget.dataset.mode;
    wx.navigateTo({
      url: "../camera-capture/camera-capture?mode=" + mode
    });
  },

  // 相机拍照结果回调 - 接收临时文件信息
  onCameraPhotoResult(result) {
    if (result && result.tempFilePath) {
      // 防止在上传过程中添加新文件
      if (this.data.isUploading) {
        wx.showModal({
          title: '提示',
          content: '正在上传文件，请等待上传完成后再添加新照片',
          showCancel: false
        });
        return;
      }

      // 将临时文件路径添加到images数组中，等待提交时上传
      const images = this.data.images.concat([result.tempFilePath]);
      this.setData({
        images,
        hasUnsavedData: true // 标记有未保存数据
      });

      // 保存状态
      this.savePageState();

      wx.showToast({
        title: '照片拍摄成功',
        icon: 'success'
      });
    }
  },

  // 相机录像结果回调 - 接收临时文件信息
  onCameraVideoResult(result) {
    if (result && result.tempFilePath) {
      // 防止在上传过程中添加新文件
      if (this.data.isUploading) {
        wx.showModal({
          title: '提示',
          content: '正在上传文件，请等待上传完成后再录制新视频',
          showCancel: false
        });
        return;
      }

      const videoDurationManager = require('../utils/video-duration-manager');

      // 获取视频时长
      const duration = videoDurationManager.processDuration(result.duration);

      // 验证视频时长
      if (duration > 0) {
        const validationResult = videoDurationManager.validateDuration(duration, this.data.videoDurationSettings);
        if (!validationResult.isValid) {
          videoDurationManager.showValidationError(validationResult);
          return;
        }
      }

      // 创建视频对象并添加到videos数组中，等待提交时上传
      const videoItem = {
        src: result.tempFilePath,
        duration: duration,
        size: result.size || 0,
        fileType: "video"
      };

      // 只允许一个视频，替换现有视频
      this.setData({
        videos: [videoItem],
        hasUnsavedData: true // 标记有未保存数据
      });

      // 保存状态
      this.savePageState();

      wx.showToast({
        title: '视频录制成功',
        icon: 'success'
      });
    }
  },

  // 预览图片
  onPreviewImage(e) {
    const src = e.currentTarget.dataset.src;

    wx.previewImage({
      current: src,
      urls: this.data.images.length > 0 ? this.data.images : [src],
      fail: (error) => {
        console.error('预览图片失败:', error);
        wx.showToast({
          title: '预览失败',
          icon: 'none'
        });
      }
    });
  },

  // 构建图片URL
  buildImageUrl(url) {
    const app = getApp();
    return ImageHelper.buildImageUrl(url, app.globalData.baseUrl);
  },

  // 录制视频 - 直接跳转到相机页面
  onRecordVideo() {
    // 跳转到相机录制页面
    wx.navigateTo({
      url: '../camera-capture/camera-capture?mode=video'
    });
  },



  // 处理视频文件，获取时长信息
  processVideoFile(video) {
    const videoDurationManager = require('../utils/video-duration-manager');

    // 获取视频时长
    const duration = videoDurationManager.processDuration(video.duration);

    // 验证视频时长
    if (duration > 0) {
      const validationResult = videoDurationManager.validateDuration(duration, this.data.videoDurationSettings);
      if (!validationResult.isValid) {
        videoDurationManager.showValidationError(validationResult);
        return;
      }
    }

    // 创建视频对象
    const videoItem = {
      src: video.tempFilePath,
      poster: video.thumbTempFilePath,
      duration: duration,
      originalDuration: video.duration, // 保存原始时长用于调试
      size: video.size,
      fileType: video.fileType || "video",
    };

    // 直接添加到列表，不再尝试复杂的获取方法
    const videos = this.data.videos.concat([videoItem]);
    this.setData({ videos });

    // 显示成功提示
    wx.showToast({
      title: "视频录制成功",
      icon: "success",
      duration: 2000,
    });
  },

  // 切换录音状态
  onToggleRecord() {
    // 防止在上传过程中录音
    if (this.data.isUploading) {
      wx.showModal({
        title: '提示',
        content: '正在上传文件，请等待上传完成后再录音',
        showCancel: false
      });
      return;
    }

    if (this.data.recording) {
      this.stopRecord();
    } else {
      this.startRecord();
    }
  },

  // 开始录音
  startRecord() {
    const recorderManager = wx.getRecorderManager();

    recorderManager.start({
      duration: 60000, // 最大60秒
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 96000,
      format: "mp3",
    });

    this.setData({
      recording: true,
      recordTime: 0,
    });

    // 开始计时
    const timer = setInterval(() => {
      const recordTime = this.data.recordTime + 1;
      this.setData({ recordTime });

      if (recordTime >= 60) {
        this.stopRecord();
      }
    }, 1000);

    this.setData({ recordTimer: timer });

    recorderManager.onStop((res) => {
      const audios = this.data.audios.concat([
        {
          tempFilePath: res.tempFilePath,
          duration: Math.round(res.duration / 1000),
          isPlaying: false,
        },
      ]);
      const playingStates = this.data.playingStates.concat([false]);
      this.setData({
        audios,
        playingStates,
        hasUnsavedData: true // 标记有未保存数据
      });

      // 保存状态
      this.savePageState();

      wx.showToast({
        title: '录音完成',
        icon: 'success'
      });
    });
  },

  // 停止录音
  stopRecord() {
    const recorderManager = wx.getRecorderManager();
    recorderManager.stop();

    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }

    this.setData({
      recording: false,
      recordTime: 0,
      recordTimer: null,
    });
  },

  // 播放音频
  onPlayAudio(e) {
    const index = e.currentTarget.dataset.index;
    const isCurrentlyPlaying = this.data.currentPlayingIndex === index;

    if (isCurrentlyPlaying) {
      // 如果当前音频正在播放，则暂停
      this.pauseAudio(index);
    } else {
      // 停止其他音频播放
      this.stopAllAudio();
      // 播放当前音频
      this.playAudio(index);
    }
  },

  // 播放指定音频
  playAudio(index) {
    const audio = this.data.audios[index];
    const innerAudioContext = wx.createInnerAudioContext();
    innerAudioContext.src = audio.tempFilePath;

    // 设置播放状态
    this.setAudioPlayingState(index, true, innerAudioContext);

    // 监听播放结束和错误
    innerAudioContext.onEnded(() => this.setAudioPlayingState(index, false));
    innerAudioContext.onError((error) => {
      console.error("音频播放错误:", error);
      this.setAudioPlayingState(index, false);
      wx.showToast({ title: "播放失败", icon: "none" });
    });

    innerAudioContext.play();
  },

  // 暂停音频
  pauseAudio(index) {
    if (this.data.audioContext) {
      this.data.audioContext.pause();
    }
    this.setAudioPlayingState(index, false);
  },

  // 统一的音频播放状态设置
  setAudioPlayingState(index, isPlaying, audioContext = null) {
    const playingStates = this.data.playingStates.slice();
    playingStates[index] = isPlaying;

    this.setData({
      playingStates,
      currentPlayingIndex: isPlaying ? index : -1,
      audioContext: isPlaying ? audioContext : null,
    });
  },

  // 停止所有音频播放
  stopAllAudio() {
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }

    // 重置所有播放状态
    const playingStates = this.data.playingStates.map(() => false);
    this.setData({
      currentPlayingIndex: -1,
      audioContext: null,
      playingStates,
    });
  },



  // 提交现场信息反馈记录
  async onSubmit() {
    // 检查是否已超时
    if (this.data.isTimeout) {
      wx.showModal({
        title: '填写超时',
        content: '现场信息反馈填写已超时，请重新选择任务单',
        showCancel: false,
        confirmText: '确定',
        success: () => {
          this.returnToPreviousPage();
        }
      });
      return;
    }

    const { currentTask, feedbackForm, images, videos, audios } = this.data;

    if (!currentTask) {
      wx.showToast({
        title: "请先选择任务单",
        icon: "none",
      });
      return;
    }

    if (!feedbackForm.category) {
      wx.showToast({
        title: "请选择反馈类别",
        icon: "none",
      });
      return;
    }

    if (!feedbackForm.feedback_time) {
      wx.showToast({
        title: "请填写反馈时间",
        icon: "none",
      });
      return;
    }

    // 检查位置信息状态 - 必须有位置信息才能提交
    if (feedbackForm.location_status !== 'authorized' || !feedbackForm.latitude || !feedbackForm.longitude) {
      wx.showModal({
        title: '位置信息缺失',
        content: '提交反馈需要位置信息。请点击"重新获取位置"按钮获取当前位置。',
        confirmText: '重新获取位置',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 尝试重新获取位置
            this.requestLocation();
          }
        }
      });
      return;
    }

    // 防止重复提交
    if (this.data.submitting || this.data.isUploading) {
      wx.showToast({
        title: "正在提交中，请稍候",
        icon: "none",
      });
      return;
    }

    // 设置提交状态
    this.setData({
      submitting: true,
      isUploading: true,
      uploadProgress: 0,
      hasUnsavedData: false // 开始提交后清除未保存标记
    });

    try {
      // 显示上传进度
      const totalFiles = images.length + videos.length + audios.length;
      let uploadedFiles = 0;

      const updateProgress = () => {
        uploadedFiles++;
        const progress = Math.round((uploadedFiles / totalFiles) * 100);

        // 更新页面状态
        this.setData({ uploadProgress: progress });

        wx.showLoading({
          title: `上传中 ${progress}%`,
          mask: true
        });
      };

      if (totalFiles > 0) {
        wx.showLoading({
          title: "开始上传文件...",
          mask: true
        });

        // 添加页面刷新保护
        this.managePageProtection(true);
      }



      // 准备多媒体文件数据
      const mediaFiles = [];
      const uploadErrors = [];

      // 上传所有图片（包括相机拍摄和相册选择的）
      for (let i = 0; i < images.length; i++) {
        try {
          // 检查文件大小
          const fileInfo = await this.getFileInfo(images[i]);
          if (fileInfo.size > 10 * 1024 * 1024) { // 10MB
            console.warn(`图片文件过大 (${(fileInfo.size / 1024 / 1024).toFixed(2)}MB)`);
          }

          const uploadRes = await this.uploadFile(images[i], "image");
          if (uploadRes) {
            mediaFiles.push({
              file_type: "image",
              file_name: uploadRes.filename,
              file_path: uploadRes.url,
              file_size: uploadRes.size,
            });
          }
          updateProgress();
        } catch (error) {
          uploadErrors.push(this.handleUploadError(error, '图片', i));
        }
      }

      // 上传所有视频（包括相机录制和相册选择的）
      for (let i = 0; i < videos.length; i++) {
        try {
          // 检查视频文件大小
          const fileInfo = await this.getFileInfo(videos[i].src);
          if (fileInfo.size > 50 * 1024 * 1024) { // 50MB
            console.warn(`视频文件过大 (${(fileInfo.size / 1024 / 1024).toFixed(2)}MB)`);
          }

          const uploadRes = await this.uploadFile(videos[i].src, "video");
          if (uploadRes) {
            mediaFiles.push({
              file_type: "video",
              file_name: uploadRes.filename,
              file_path: uploadRes.url,
              file_size: uploadRes.size,
              duration: videos[i].duration,
            });
          }
          updateProgress();
        } catch (error) {
          uploadErrors.push(this.handleUploadError(error, '视频', i));
        }
      }

      // 上传音频
      for (let i = 0; i < audios.length; i++) {
        try {

          const uploadRes = await this.uploadFile(audios[i].tempFilePath, "audio");
          if (uploadRes) {
            mediaFiles.push({
              file_type: "audio",
              file_name: uploadRes.filename,
              file_path: uploadRes.url,
              file_size: uploadRes.size,
              duration: audios[i].duration,
            });
          }
          updateProgress();
        } catch (error) {
          uploadErrors.push(this.handleUploadError(error, '音频', i));
        }
      }

      // 如果有上传错误，显示警告但继续提交
      if (uploadErrors.length > 0) {
        console.warn("部分文件上传失败");
        wx.hideLoading();

        const result = await new Promise((resolve) => {
          wx.showModal({
            title: "部分文件上传失败",
            content: `${uploadErrors.length}个文件上传失败，是否继续提交？\n\n失败文件：\n${uploadErrors.slice(0, 3).join('\n')}${uploadErrors.length > 3 ? '\n...' : ''}`,
            confirmText: "继续提交",
            cancelText: "取消",
            success: (res) => resolve(res.confirm)
          });
        });

        if (!result) {
          this.setData({ submitting: false });
          return;
        }
      } else if (totalFiles > 0) {
        wx.hideLoading();
      }

      // 提交现场信息反馈记录

      wx.showLoading({
        title: "提交中...",
        mask: true
      });

      const app = getApp();

      const res = await app.request({
        url: "/api/feedbacks",
        method: "POST",
        data: Object.assign({
          task_number: currentTask.id, // 修复字段名：使用task_number而不是task_id
          media_files: mediaFiles,
        }, feedbackForm),
      });

      wx.hideLoading();

      if (res.data.success) {
        // 停止超时计时器
        if (this.data.timeoutTimer) {
          clearInterval(this.data.timeoutTimer);
        }

        // 标记为提交成功，清除上传状态
        this.setData({
          submitting: true,
          isUploading: false,
          uploadProgress: 100,
          submitSuccess: true,
          hasUnsavedData: false,
          timeoutEnabled: false // 提交成功后隐藏倒计时
        });

        // 清除页面状态
        this.clearPageState();

        // 移除页面刷新保护
        this.managePageProtection(false);


        // 立即执行跳转逻辑，不使用延迟
        this.handleSuccessNavigation();
      } else {
        console.error("服务器返回错误:", res.data);
        wx.showModal({
          title: "提交失败",
          content: res.data.message || "服务器返回未知错误",
          showCancel: false
        });
      }
    } catch (error) {
      wx.hideLoading();
      console.error("提交现场信息反馈记录失败:", error);

      // 移除页面刷新保护
      this.managePageProtection(false);

      const errorMessage = this.handleNetworkError(error, '提交现场信息反馈记录');
      wx.showModal({
        title: "提交失败",
        content: errorMessage,
        showCancel: false
      });
    } finally {
      // 只有在提交失败时才重置状态
      if (!this.data.submitSuccess) {
        this.setData({
          submitting: false,
          isUploading: false,
          uploadProgress: 0,
          hasUnsavedData: true // 失败后重新标记为有未保存数据
        });
      }
    }
  },

  // 加载视频时长设置
  async loadVideoDurationSettings() {
    const videoDurationManager = require('../utils/video-duration-manager');

    try {
      const settings = await videoDurationManager.loadSettings();
      this.setData({
        videoDurationSettings: settings,
      });
      console.log("反馈页面视频时长设置加载完成:", settings);
    } catch (error) {
      console.error("加载视频时长设置失败:", error);
      // 确保总是有设置值，避免显示"加载中"
      const defaultSettings = videoDurationManager.getSettings();
      this.setData({
        videoDurationSettings: defaultSettings,
      });
      console.log("使用默认视频时长设置:", defaultSettings);
    }
  },

  // 处理提交成功后的页面跳转
  handleSuccessNavigation() {
    
    wx.showToast({
      title: "提交成功",
      icon: "success",
      duration: 2000
    });
    console.log('开始处理提交成功后的页面跳转');

    // 检查页面栈
    const pages = getCurrentPages();
    console.log('当前页面栈长度:', pages.length);

    if (pages.length > 1) {
      // 有上一页，尝试返回
      console.log('尝试返回上一页');
      wx.navigateBack({
        success: () => {
          console.log('返回上一页成功');
        },
        fail: (error) => {
          console.error('返回上一页失败:', error);
          // 返回失败，跳转到任务单列表
          this.navigateToTaskList();
        }
      });
    } else {
      // 没有上一页，直接跳转到任务单列表
      console.log('没有上一页，跳转到任务单列表');
      this.navigateToTaskList();
    }
  },

  // 跳转到任务单列表页面
  navigateToTaskList() {
    console.log('跳转到任务单列表页面');
    const currentTask = this.data.currentTask;

    if (currentTask && currentTask.project_id) {
      const url = `/subpackages/task-management/task-list/task-list?projectId=${currentTask.project_id}`;
      console.log('跳转URL:', url);

      wx.redirectTo({
        url: url,
        success: () => {
          console.log('跳转到任务单列表成功');
        },
        fail: (error) => {
          console.error('跳转到任务单列表失败:', error);
          // 跳转失败，回到首页
          wx.switchTab({
            url: '/pages/index/index',
            success: () => {
              console.log('回到首页成功');
            },
            fail: (error) => {
              console.error('回到首页失败:', error);
            }
          });
        }
      });
    } else {
      console.log('没有当前任务信息，回到首页');
      wx.switchTab({
        url: '/pages/index/index',
        success: () => {
          console.log('回到首页成功');
        },
        fail: (error) => {
          console.error('回到首页失败:', error);
        }
      });
    }
  },

  // 构建上传请求头
  buildUploadHeaders() {
    const header = {};
    const currentCompany = wx.getStorageSync("currentCompany");
    const userInfo = wx.getStorageSync("userInfo");

    // 生成认证token
    if (userInfo && userInfo.PersonId) {
      try {
        const userId = userInfo.PersonId;
        const username = userInfo.PersonName;
        const tokenData = `${userId}:${username}:${Date.now()}`;
        const token = getApp().base64Encode(tokenData);
        header["authorization"] = token;
      } catch (error) {
        console.warn('生成认证信息失败:', error);
      }
    }

    // 添加用户信息
    if (userInfo) {
      try {
        const userInfoJson = JSON.stringify({
          PersonId: userInfo.PersonId,
          PersonName: userInfo.PersonName,
          Phone: userInfo.Phone
        });
        header["X-User-Info"] = encodeURIComponent(userInfoJson);
      } catch (error) {
        console.warn('序列化用户信息失败:', error);
      }
    }

    // 添加公司信息
    if (currentCompany) {
      const companyJson = JSON.stringify(currentCompany);
      header["X-Current-Company"] = encodeURIComponent(companyJson);
    }

    return header;
  },

  // 显示上传提示
  showUploadTips() {
    wx.showModal({
      title: "上传提示",
      content: "为提高上传成功率，建议：\n\n1. 使用稳定的WiFi网络\n2. 单个文件不超过10MB\n3. 视频建议压缩后上传\n4. 避免在网络信号弱的地方上传\n5. 上传过程中不要切换网络",
      confirmText: "知道了",
      showCancel: false
    });
  },


  // 获取文件信息
  async getFileInfo(filePath) {
    return new Promise((resolve) => {
      wx.getFileInfo({
        filePath: filePath,
        success: (res) => {
          resolve({
            size: res.size,
            digest: res.digest
          });
        },
        fail: (error) => {
          console.warn("获取文件信息失败:", error);
          // 如果获取失败，返回默认值
          resolve({ size: 0 });
        }
      });
    });
  },

  // 上传文件（带重试机制）
  async uploadFile(filePath, type, retryCount) {
    if (retryCount === undefined) {
      retryCount = 0;
    }
    const maxRetries = 5; // 增加重试次数到5次
    const baseRetryDelay = 2000; // 增加基础延迟到2秒

    return new Promise((resolve, reject) => {
      const app = getApp();
      const header = this.buildUploadHeaders();

      // 检查网络状态
      wx.getNetworkType({
        success: (networkRes) => {
          if (networkRes.networkType === 'none') {
            reject(new Error('网络连接不可用，请检查网络设置'));
            return;
          }
        }
      });

      const uploadTask = wx.uploadFile({
        url: `${app.globalData.feedbackBaseUrl}/api/upload/single`,
        filePath: filePath,
        name: "file",
        header: header,
        timeout: 90000, // 增加超时时间到90秒
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve(data.data);
            } else {
              console.error("上传失败:", data.message);
              reject(new Error(data.message));
            }
          } catch (error) {
            console.error("解析上传响应失败:", error.message);
            reject(error);
          }
        },
        fail: (error) => {
          console.error(`上传失败:`, error.errMsg || error.message);

          // 检查是否是网络错误且还有重试次数
          if (retryCount < maxRetries &&
              (error.errMsg.includes("ECONNRESET") ||
               error.errMsg.includes("timeout") ||
               error.errMsg.includes("network") ||
               error.errMsg.includes("ENOTFOUND") ||
               error.errMsg.includes("ETIMEDOUT") ||
               error.errMsg.includes("fail"))) {

            // 使用指数退避算法计算延迟时间
            const retryDelay = baseRetryDelay * Math.pow(2, retryCount) + Math.random() * 1000;


            setTimeout(async () => {
              try {
                const result = await this.uploadFile(filePath, type, retryCount + 1);
                resolve(result);
              } catch (retryError) {
                reject(retryError);
              }
            }, retryDelay);
          } else {
            reject(error);
          }
        },
      });

      // 监听上传进度
      uploadTask.onProgressUpdate(() => {
        // 可以在这里添加更详细的进度显示逻辑
      });
    });
  },


});
