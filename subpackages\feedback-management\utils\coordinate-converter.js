/**
 * 坐标系转换工具
 * 处理不同坐标系之间的转换：WGS84、GCJ02、BD09
 */

const PI = Math.PI;
const X_PI = PI * 3000.0 / 180.0;
const A = 6378245.0;
const EE = 0.00669342162296594323;

/**
 * 判断坐标是否在中国境内
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {boolean} 是否在中国境内
 */
function isInChina(lng, lat) {
  return lng >= 72.004 && lng <= 137.8347 && lat >= 0.8293 && lat <= 55.8271;
}

/**
 * WGS84 转 GCJ02
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {Object} 转换后的坐标 {lng, lat}
 */
function wgs84ToGcj02(lng, lat) {
  if (!isInChina(lng, lat)) {
    return { lng, lat };
  }
  
  let dlat = transformLat(lng - 105.0, lat - 35.0);
  let dlng = transformLng(lng - 105.0, lat - 35.0);
  
  const radlat = lat / 180.0 * PI;
  let magic = Math.sin(radlat);
  magic = 1 - EE * magic * magic;
  const sqrtmagic = Math.sqrt(magic);
  
  dlat = (dlat * 180.0) / ((A * (1 - EE)) / (magic * sqrtmagic) * PI);
  dlng = (dlng * 180.0) / (A / sqrtmagic * Math.cos(radlat) * PI);
  
  const mglat = lat + dlat;
  const mglng = lng + dlng;
  
  return { lng: mglng, lat: mglat };
}

/**
 * GCJ02 转 WGS84
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {Object} 转换后的坐标 {lng, lat}
 */
function gcj02ToWgs84(lng, lat) {
  if (!isInChina(lng, lat)) {
    return { lng, lat };
  }
  
  let dlat = transformLat(lng - 105.0, lat - 35.0);
  let dlng = transformLng(lng - 105.0, lat - 35.0);
  
  const radlat = lat / 180.0 * PI;
  let magic = Math.sin(radlat);
  magic = 1 - EE * magic * magic;
  const sqrtmagic = Math.sqrt(magic);
  
  dlat = (dlat * 180.0) / ((A * (1 - EE)) / (magic * sqrtmagic) * PI);
  dlng = (dlng * 180.0) / (A / sqrtmagic * Math.cos(radlat) * PI);
  
  const mglat = lat - dlat;
  const mglng = lng - dlng;
  
  return { lng: mglng, lat: mglat };
}

/**
 * GCJ02 转 BD09
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {Object} 转换后的坐标 {lng, lat}
 */
function gcj02ToBd09(lng, lat) {
  const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * X_PI);
  const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * X_PI);
  
  const bd_lng = z * Math.cos(theta) + 0.0065;
  const bd_lat = z * Math.sin(theta) + 0.006;
  
  return { lng: bd_lng, lat: bd_lat };
}

/**
 * BD09 转 GCJ02
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {Object} 转换后的坐标 {lng, lat}
 */
function bd09ToGcj02(lng, lat) {
  const x = lng - 0.0065;
  const y = lat - 0.006;
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * X_PI);
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * X_PI);
  
  const gcj_lng = z * Math.cos(theta);
  const gcj_lat = z * Math.sin(theta);
  
  return { lng: gcj_lng, lat: gcj_lat };
}

/**
 * WGS84 转 BD09
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {Object} 转换后的坐标 {lng, lat}
 */
function wgs84ToBd09(lng, lat) {
  const gcj = wgs84ToGcj02(lng, lat);
  return gcj02ToBd09(gcj.lng, gcj.lat);
}

/**
 * BD09 转 WGS84
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {Object} 转换后的坐标 {lng, lat}
 */
function bd09ToWgs84(lng, lat) {
  const gcj = bd09ToGcj02(lng, lat);
  return gcj02ToWgs84(gcj.lng, gcj.lat);
}

/**
 * 纬度转换
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {number} 转换后的纬度
 */
function transformLat(lng, lat) {
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0;
  ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0;
  return ret;
}

/**
 * 经度转换
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @returns {number} 转换后的经度
 */
function transformLng(lng, lat) {
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
  ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;
  ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0;
  ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0;
  return ret;
}

/**
 * 为天地图API准备坐标（转换为WGS84）
 * @param {number} lng 经度
 * @param {number} lat 纬度
 * @param {string} sourceType 源坐标系类型：'gcj02', 'bd09', 'wgs84'
 * @returns {Object} WGS84坐标 {lng, lat}
 */
function prepareForTianditu(lng, lat, sourceType = 'gcj02') {
  switch (sourceType) {
    case 'bd09':
      return bd09ToWgs84(lng, lat);
    case 'gcj02':
      return gcj02ToWgs84(lng, lat);
    case 'wgs84':
    default:
      return { lng, lat };
  }
}

/**
 * 计算两点间距离（米）
 * @param {number} lng1 点1经度
 * @param {number} lat1 点1纬度
 * @param {number} lng2 点2经度
 * @param {number} lat2 点2纬度
 * @returns {number} 距离（米）
 */
function getDistance(lng1, lat1, lng2, lat2) {
  const radLat1 = lat1 * PI / 180.0;
  const radLat2 = lat2 * PI / 180.0;
  const a = radLat1 - radLat2;
  const b = lng1 * PI / 180.0 - lng2 * PI / 180.0;
  
  let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + 
    Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
  s = s * 6378.137;
  s = Math.round(s * 10000) / 10000;
  s = s * 1000;
  
  return s;
}

module.exports = {
  isInChina,
  wgs84ToGcj02,
  gcj02ToWgs84,
  gcj02ToBd09,
  bd09ToGcj02,
  wgs84ToBd09,
  bd09ToWgs84,
  prepareForTianditu,
  getDistance
};
