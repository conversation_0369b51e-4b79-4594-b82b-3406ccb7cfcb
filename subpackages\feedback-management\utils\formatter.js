/**
 * 格式化工具类
 * 提供各种数据格式化方法
 */

class Formatter {
  /**
   * 格式化日期时间
   * @param {Date|string|number} date - 日期对象、时间戳或日期字符串
   * @param {string} format - 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
   * @returns {string} 格式化后的日期字符串
   */
  static formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!date) return '';

    let dateObj;
    if (date instanceof Date) {
      dateObj = date;
    } else if (typeof date === 'string' || typeof date === 'number') {
      // 处理SQL Server返回的日期字符串，确保正确解析
      if (typeof date === 'string') {
        // 如果是ISO格式的字符串，直接使用
        if (date.includes('T') || date.includes('Z')) {
          dateObj = new Date(date);
        } else {
          // 如果是 'YYYY-MM-DD HH:mm:ss' 格式，需要确保正确解析
          // 在字符串后添加时区信息，避免时区偏移问题
          const dateStr = date.replace(' ', 'T');
          dateObj = new Date(dateStr);

          // 如果解析失败，尝试其他方式
          if (isNaN(dateObj.getTime())) {
            dateObj = new Date(date);
          }
        }
      } else if (typeof date === 'number') {
        // 处理数字类型的日期
        // 如果是0或负数，直接返回空字符串
        if (date <= 0) {
          return '';
        }
        // 如果是小于合理时间戳范围的数字，可能是错误数据，返回空字符串
        // 使用1990年1月1日作为最小有效时间戳 (631152000000)
        if (date < 631152000000) {
          console.warn('formatDateTime: 检测到可能的无效数字日期:', date);
          return '';
        }
        dateObj = new Date(date);
      }
    } else {
      return '';
    }

    if (isNaN(dateObj.getTime())) {
      console.warn('formatDateTime: 无法解析的日期:', date);
      return '';
    }

    // 额外检查：如果解析出的年份小于1990年，可能是错误数据
    const year = dateObj.getFullYear();
    if (year < 1990) {
      console.warn('formatDateTime: 检测到异常年份日期，可能是错误数据:', date, '年份:', year);
      return '';
    }
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    const seconds = String(dateObj.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  }

  /**
   * 获取当前时间的标准格式字符串
   * @param {boolean} includeSeconds - 是否包含秒，默认true
   * @returns {string} 当前时间字符串
   */
  static getCurrentDateTime(includeSeconds = true) {
    const now = new Date();
    const format = includeSeconds ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD HH:mm';
    return this.formatDateTime(now, format);
  }

  /**
   * 创建用于时间选择器的时间字符串
   * @param {number} year - 年份
   * @param {number} month - 月份（1-12）
   * @param {number} day - 日期（1-31）
   * @param {number} hour - 小时（0-23）
   * @param {number} minute - 分钟（0-59）
   * @returns {string} 格式化的时间字符串
   */
  static createTimeString(year, month, day, hour, minute) {
    const timeStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')} ${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}:00`;
    return timeStr;
  }

  /**
   * 格式化日期（不包含时间）
   * @param {Date|string|number} date - 日期
   * @returns {string} 格式化后的日期字符串
   */
  static formatDate(date) {
    return this.formatDateTime(date, 'YYYY-MM-DD');
  }

  /**
   * 格式化时间（不包含日期）
   * @param {Date|string|number} date - 日期
   * @returns {string} 格式化后的时间字符串
   */
  static formatTime(date) {
    return this.formatDateTime(date, 'HH:mm:ss');
  }

  /**
   * 格式化相对时间（如：刚刚、5分钟前、2小时前等）
   * @param {Date|string|number} date - 日期
   * @returns {string} 相对时间字符串
   */
  static formatRelativeTime(date) {
    if (!date) return '';
    
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      return '';
    }

    const now = new Date();
    const diff = now.getTime() - dateObj.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (seconds < 60) {
      return '刚刚';
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return this.formatDate(date);
    }
  }

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @param {number} decimals - 小数位数，默认2位
   * @returns {string} 格式化后的文件大小
   */
  static formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 B';
    if (!bytes || typeof bytes !== 'number') return '';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  /**
   * 格式化手机号（隐藏中间4位）
   * @param {string} phone - 手机号
   * @returns {string} 格式化后的手机号
   */
  static formatPhone(phone) {
    if (!phone || typeof phone !== 'string') return '';
    
    if (phone.length === 11) {
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
    
    return phone;
  }

  /**
   * 格式化身份证号（隐藏中间部分）
   * @param {string} idCard - 身份证号
   * @returns {string} 格式化后的身份证号
   */
  static formatIdCard(idCard) {
    if (!idCard || typeof idCard !== 'string') return '';
    
    if (idCard.length === 18) {
      return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
    }
    
    return idCard;
  }

  /**
   * 格式化金额（添加千分位分隔符）
   * @param {number|string} amount - 金额
   * @param {number} decimals - 小数位数，默认2位
   * @returns {string} 格式化后的金额
   */
  static formatMoney(amount, decimals = 2) {
    if (amount === null || amount === undefined || amount === '') return '';
    
    const num = parseFloat(amount);
    if (isNaN(num)) return '';

    return num.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  /**
   * 格式化百分比
   * @param {number} value - 数值（0-1之间）
   * @param {number} decimals - 小数位数，默认1位
   * @returns {string} 格式化后的百分比
   */
  static formatPercentage(value, decimals = 1) {
    if (value === null || value === undefined || value === '') return '';
    
    const num = parseFloat(value);
    if (isNaN(num)) return '';

    return (num * 100).toFixed(decimals) + '%';
  }

  /**
   * 截断文本（超出长度显示省略号）
   * @param {string} text - 文本
   * @param {number} maxLength - 最大长度
   * @param {string} suffix - 后缀，默认'...'
   * @returns {string} 截断后的文本
   */
  static truncateText(text, maxLength, suffix = '...') {
    if (!text || typeof text !== 'string') return '';
    
    if (text.length <= maxLength) {
      return text;
    }
    
    return text.substring(0, maxLength - suffix.length) + suffix;
  }

  /**
   * 格式化状态文本
   * @param {string|number} status - 状态值
   * @param {Object} statusMap - 状态映射表
   * @returns {string} 状态文本
   */
  static formatStatus(status, statusMap) {
    if (!statusMap || typeof statusMap !== 'object') {
      return String(status || '');
    }
    
    return statusMap[status] || String(status || '');
  }

  /**
   * 格式化数组为字符串
   * @param {Array} array - 数组
   * @param {string} separator - 分隔符，默认'、'
   * @returns {string} 格式化后的字符串
   */
  static formatArray(array, separator = '、') {
    if (!Array.isArray(array)) return '';
    
    return array.filter(item => item !== null && item !== undefined && item !== '').join(separator);
  }

  /**
   * 格式化地址（省市区拼接）
   * @param {Object} address - 地址对象
   * @param {string} address.province - 省份
   * @param {string} address.city - 城市
   * @param {string} address.district - 区县
   * @param {string} address.detail - 详细地址
   * @returns {string} 完整地址
   */
  static formatAddress(address) {
    if (!address || typeof address !== 'object') return '';
    
    const { province, city, district, detail } = address;
    const parts = [province, city, district, detail].filter(part => part && part.trim());
    
    return parts.join('');
  }

  /**
   * 格式化URL参数
   * @param {Object} params - 参数对象
   * @returns {string} URL参数字符串
   */
  static formatUrlParams(params) {
    if (!params || typeof params !== 'object') return '';

    const paramPairs = Object.keys(params)
      .filter(key => params[key] !== null && params[key] !== undefined && params[key] !== '')
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`);

    return paramPairs.length > 0 ? '?' + paramPairs.join('&') : '';
  }

  /**
   * 格式化枚举值为中文
   * @param {string|number} value - 枚举值
   * @param {Object} enumMap - 枚举映射表
   * @param {string} defaultValue - 默认值
   * @returns {string} 中文描述
   */
  static formatEnum(value, enumMap, defaultValue = '未知') {
    if (!enumMap || typeof enumMap !== 'object') {
      return defaultValue;
    }

    return enumMap[value] || defaultValue;
  }
}

module.exports = Formatter;
