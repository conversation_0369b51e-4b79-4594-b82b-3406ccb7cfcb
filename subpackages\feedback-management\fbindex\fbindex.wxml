<view class="container">
  <!-- 头部信息 -->
  <view class="header">
    <view class="user-info">
      <text class="welcome">{{userInfo.username}}</text>
      <text class="phone" wx:if="{{userInfo && userInfo.phone}}">手机号:{{userInfo.phone}}</text>
      <text class="company-name" wx:if="{{currentCompany}}">公司:{{currentCompany.name}}</text>
      <text class="no-company" wx:else bindtap="onSelectCompany">请选择公司</text>
    </view>
  </view>
  <!-- 统计卡片 -->
  <view class="stats-grid">
    <view class="stat-card">
      <text class="stat-number">{{stats.totalProjects || 0}}</text>
      <text class="stat-label">工程数量</text>
    </view>
    <view class="stat-card">
      <text class="stat-number">{{stats.totalTasks || 0}}</text>
      <text class="stat-label">任务单数</text>
    </view>
  </view>
  <!-- 反馈单列表按钮 -->
  <view class="feedback-list-button" bindtap="goToFeedbackList">
    <text class="feedback-list-text">反馈单列表</text>
  </view>
  <!-- 当前公司工程列表 -->
  <view class="recent-projects" wx:if="{{currentCompany}}">
    <view class="section-header">
      <text class="section-title">新建现场信息反馈-选择工程</text>
      <view class="header-actions">
        <text class="view-all" wx:if="{{projectsTotal > recentProjects.length}}" bindtap="onViewAllProjects">
          查看全部
        </text>
      </view>
    </view>
    <!-- 搜索栏 -->
    <view class="search-bar">
      <input class="search-input" placeholder="搜索工程名称、项目公司或施工单位" value="{{searchKeyword}}" bindinput="onSearchInput" />
    </view>
    <!-- 工程列表 -->
    <view class="project-list" wx:if="{{recentProjects.length > 0}}">
      <view class="project-item" wx:for="{{recentProjects}}" wx:key="id" bindtap="onProjectTap" data-project="{{item}}">
        <view class="project-info">
          <view class="project-main">
            <text class="project-name">工程名称：{{item.name}}</text>
            <text class="project-code" wx:if="{{item.code}}">工程编号：{{item.code}}</text>
          </view>
          <text class="project-unit" wx:if="{{item.construction_unit}}">
            施工单位：{{item.construction_unit}}
          </text>
          <view class="project-meta">
            <text class="task-count">{{item.taskCountText}}</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{recentProjects.length === 0 && !loading}}">
      <view class="empty-icon">📋</view>
      <text class="empty-text">暂无工程</text>
    </view>
    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>
  </view>
  <!-- 未选择公司提示 -->
  <view class="no-company-tip" wx:if="{{!currentCompany}}">
    <view class="tip-icon">🏢</view>
    <text class="tip-text">请先选择公司</text>
    <text class="tip-desc">选择公司后即可查看该公司的工程列表</text>
    <button class="btn btn-primary" bindtap="onSelectCompany">选择公司</button>
  </view>
</view>