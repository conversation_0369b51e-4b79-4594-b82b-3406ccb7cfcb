[2025-08-05 00:28:00] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 00:28:00] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 00:28:00] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 00:52:25] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 00:52:25] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 01:42:04] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 01:42:04] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 02:52:59] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 02:52:59] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 02:54:52] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":6060,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
[2025-08-05 03:33:26] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":6061,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
[2025-08-05 05:32:16] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 05:32:16] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 05:35:21] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":6062,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
[2025-08-05 05:36:28] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":6063,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
[2025-08-05 05:38:37] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":6064,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
[2025-08-05 05:47:14] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":6065,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
