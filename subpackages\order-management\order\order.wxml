<!-- wxml文件 -->
<view>
    <view class="navigaterbar">
        <view class="currentbar" wx:for="{{FixStateTab}}" wx:key="index">
        <view bindtap="onNaTabClick" data-item="{{item}}" style="background-color: {{item.nvBackcolor}};color: {{item.nvcolor}};">{{item.name}}</view>
    </view>
    </view>
    <view class="addButtonArea">
        <button type="primary" bindtap="onNewOrderclick">新增订单</button>
    </view>
    <view class="inTitle">订单列表</view>
    <view class="OrderList">
        <block class="OrderCar" wx:for="{{FromList}}" wx:key="index">
            <CarOrderCard-Comp CarCard="{{item}}"></CarOrderCard-Comp>
        </block>
    </view>

</view>