/* pages/orderDetail/orderDetail.wxss *//* components/CarOrder/CarOrderCard/CarOrderCard.wxss */
page{
    width: 100%;
    height: 100%;
    background-color:rgb(223, 223, 223);
}
.CarCard{
    margin: 0;
    font-size:larger;
}
.inTitle{
    margin-top: 10rpx;
    margin-left: 12rpx;
}
.Info{
    margin-left: 10rpx;
    margin-right: 10rpx;
    padding: 10rpx;
    padding-top: 20rpx;
    border: 2px double grey;
    background-color: white;
}
.TextStyle{
    color:"#1296db";
}
.buttonArea
{
    margin-top: 30rpx;
    display: flex;
    justify-content: space-around;
}
.titleinput
{
    margin-bottom: 20rpx;
}
.titleinput view
{
    height: 50rpx;
    line-height: 50rpx;
}
.titleinput input
{
    height: 50rpx;
    line-height: 50rpx;
    border: 1px solid black;
}