<!--pages/orderDetail/orderDetail.wxml-->
<view class="CarCard">
    <view class="inTitle">基础信息</view>
    <view class="Info">
        <view> <text space="ensp">单据编号: {{ShowDetail.BillNo}}</text></view>
        <view space="ensp">单据日期: {{ShowDetail.BillDate}}</view>
        <view space="ensp">公司名称: {{ShowDetail.CompName}}</view>
    </view>
    <view class="inTitle">车辆信息</view>
    <view class="Info">
        <view class="titleinput">
            <text space="ensp" style="line-height: 50rpx;">    车号: </text>
            <input type="text" value="{{ShowDetail.CarMark}}" disabled="true" style="line-height: 50rpx;"/>
        </view>
        <view class="titleinput">
            <text space="ensp">  车牌号: {{ShowDetail.CarName}}</text>
        </view>

        <view class="titleinput">
            <text space="ensp" style="line-height: 50rpx;">行驶公里: </text>
            <input type="text" value="{{ShowDetail.TotalMileage}}" disabled="true" style="line-height: 50rpx;"/>
        </view>
        <view class="titleinput">
            报修项目:  <text space="ensp" wx:for="{{ShowDetail.repairProjectDetail}}" wx:key="index"> {{item.RepairProjectName}}</text> 
        </view>
        <view class="titleinputReason">
            <text space="ensp">维修原因: </text>
            <textarea disabled="true" style="border:1px solid black;height: 250rpx;width: 500rpx;" value="{{ShowDetail.ServReason}}"></textarea>>
        </view>
        <view>
            <text space="ensp">申报人员: {{ShowDetail.WxOperaPersonName}} </text>
            <text space="ensp">    审核状态: {{ShowDetail.PermitStateName}}</text>
        </view>
        <view>
            <text space="ensp">联系电话: {{ShowDetail.OperaPhone}}</text>
        </view>
    </view>
    <view class="buttonArea">
        <button type="warn" size="mini" bindtap="onDeleteOrderclick">删除订单</button>
        <button size="mini" bindtap="onUpdateOrderclick">修改订单</button>
        <button type="primary" size="mini" bindtap="onNewOrderclick">新增订单</button>
    </view>
    <view class="inTitle">车队管理审批信息</view>
    <view class="Info">
    <view>
        <text space="ensp">审批状态: {{ShowDetail.IsApproveName}}</text>
        <text space="ensp">     审批人员: {{ShowDetail.PermitterName}}</text>
    </view>
        <text space="ensp">审批意见: {{ShowDetail.ApprovalOpinions}}</text>
    </view>
    <view class="buttonArea">
        <button type="warn" size="mini" bindtap="onCarLeaderclick">车队长审批</button>
        <button type="primary" size="mini" bindtap="onChosePersonclick">选择维修人</button>
    </view>
    <view class="inTitle">维修主管维修信息</view>
    <view class="Info">
        <view space="ensp">维修状态: {{ShowDetail.FixStateName}}</view>
        <text space="ensp">维修人员: {{ShowDetail.FixPersonName}}</text>
    </view>
    <custom-showInpute modal-show="{{showModal}}" bind:confirm="confirmHandler"></custom-showInpute>
</view>