/* pages/Login/Login.wxss */
page {
    background-color: rgb(223, 223, 223);
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

/* 主容器 */
.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    width: 100%;
    background: transparent;
}

/* 整体渐变背景区域 */
.content-area {
    flex: 1;
    background: linear-gradient(to bottom, #1296DB, #FFFFFF);
    min-height: 100vh;
    padding: 80rpx 5% 40rpx 5%;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
}

/* 标题区域样式 */
.title-section {
    text-align: center;
    color: white;
    margin-bottom: 60rpx;
    width: 100%;
}

.app-title {
    margin: 0;
    font-size: 48rpx;
    line-height: 1.4;
    font-weight: bold;
    color: white;
}

/* 登录表单容器 */
.login-form {
    width: 100%;
    max-width: 600rpx;
    display: flex;
    justify-content: center;
    flex: 1;
}

/* 表单卡片 */
.form-card {
    width: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10rpx);
    box-sizing: border-box;
    margin: 0 auto;
}

/* 表单区域 */
.form-section {
    margin-bottom: 30rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 15rpx;
}

/* 位置权限样式 */
.location-permission-info {
    background-color: #f8f9fa;
    border-radius: 12rpx;
    padding: 20rpx;
    border: 2rpx solid #e9ecef;
}

.permission-desc {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
    display: block;
    margin-bottom: 15rpx;
}

.permission-status {
    font-size: 26rpx;
    font-weight: 500;
    padding: 8rpx 12rpx;
    border-radius: 8rpx;
    display: inline-block;
}

.permission-status.checking {
    background-color: #e2e3e5;
    color: #495057;
    border: 1rpx solid #ced4da;
}

.permission-status.requesting {
    background-color: #fff3cd;
    color: #856404;
    border: 1rpx solid #ffeaa7;
}

.permission-status.granted {
    background-color: #d4edda;
    color: #155724;
    border: 1rpx solid #c3e6cb;
}

.permission-status.denied {
    background-color: #f8d7da;
    color: #721c24;
    border: 1rpx solid #f5c6cb;
}

/* 重新授权按钮 */
.retry-permission-btn {
    margin-top: 15rpx;
    width: 100%;
    height: 60rpx;
    background-color: #1296DB;
    color: white;
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-weight: bold;
}

/* 复选框样式 */
.checkbox-item {
    display: flex;
    align-items: center;
    padding: 15rpx 0;
}

.checkbox-label {
    margin-left: 15rpx;
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
}

/* 输入框样式 */
.form-input {
    width: 100%;
    height: 80rpx;
    background-color: #f8f9fa;
    border: 2rpx solid #e9ecef;
    border-radius: 12rpx;
    padding: 0 20rpx;
    font-size: 30rpx;
    color: #333;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

.form-input:focus {
    border-color: #1296DB;
    background-color: #fff;
    box-shadow: 0 0 0 4rpx rgba(18, 150, 219, 0.1);
}

.form-input::placeholder {
    color: #999;
    font-size: 28rpx;
}

/* 按钮组样式 */
.button-group {
    display: flex;
    gap: 20rpx;
    margin-top: 40rpx;
    justify-content: space-between;
}

.cancel-btn, .confirm-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 12rpx;
    font-size: 32rpx;
    font-weight: bold;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cancel-btn {
    background-color: #f8f9fa;
    color: #666;
    border: 2rpx solid #e9ecef;
}

.cancel-btn:active {
    background-color: #e9ecef;
}

.confirm-btn {
    background-color: #1296DB !important;
    color: white !important;
    border-color: #1296DB !important;
}

.confirm-btn:active {
    background-color: #0d7bc4 !important;
    border-color: #0d7bc4 !important;
}

.confirm-btn.disabled {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    opacity: 0.6;
}

.confirm-btn[disabled] {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    opacity: 0.6;
    color: white !important;
}

/* 覆盖微信小程序默认的复选框样式 */
checkbox .wx-checkbox-input {
    border-radius: 6rpx;
    width: 40rpx;
    height: 40rpx;
    border: 2rpx solid #ddd;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked {
    background-color: #1296DB !important;
    border-color: #1296DB !important;
}

checkbox .wx-checkbox-input.wx-checkbox-input-checked::before {
    color: white !important;
    font-size: 24rpx;
}

/* 响应式设计 - 小屏幕适配 */
@media (max-width: 400px) {
    .content-area {
        min-height: 100vh;
        padding: 60rpx 3% 30rpx 3%;
    }

    .title-section {
        margin-bottom: 40rpx;
    }

    .app-title {
        font-size: 36rpx;
        margin: 0;
    }

    .login-form {
        width: 100%;
    }

    .form-card {
        padding: 30rpx 25rpx;
        margin: 0 auto;
    }

    .section-title {
        font-size: 30rpx;
    }

    .form-input {
        height: 75rpx;
        font-size: 28rpx;
    }

    .cancel-btn, .confirm-btn {
        height: 75rpx;
        font-size: 30rpx;
    }
}

/* 响应式设计 - 大屏幕适配 */
@media (min-width: 800px) {
    .content-area {
        min-height: 100vh;
        padding: 100rpx 5% 60rpx 5%;
    }

    .title-section {
        margin-bottom: 80rpx;
    }

    .app-title {
        font-size: 52rpx;
        margin: 0;
    }

    .login-form {
        max-width: 500rpx;
        width: 100%;
    }

    .form-card {
        padding: 50rpx 40rpx;
        margin: 0 auto;
    }
}
  
  