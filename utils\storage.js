/**
 * 本地存储工具类
 * 封装微信小程序的本地存储操作，提供类型安全和错误处理
 */

class StorageUtil {
  /**
   * 存储数据到本地
   * @param {string} key - 存储键名
   * @param {*} data - 要存储的数据
   * @param {boolean} sync - 是否使用同步方法，默认true
   * @returns {Promise<boolean>} 存储是否成功
   */
  static setItem(key, data, sync = true) {
    if (!key || typeof key !== 'string') {
      console.error('StorageUtil.setItem: key must be a non-empty string');
      return Promise.resolve(false);
    }

    try {
      if (sync) {
        wx.setStorageSync(key, data);
        return Promise.resolve(true);
      } else {
        return new Promise((resolve) => {
          wx.setStorage({
            key,
            data,
            success: () => resolve(true),
            fail: (error) => {
              console.error('StorageUtil.setItem failed:', error);
              resolve(false);
            }
          });
        });
      }
    } catch (error) {
      console.error('StorageUtil.setItem error:', error);
      return Promise.resolve(false);
    }
  }

  /**
   * 从本地获取数据
   * @param {string} key - 存储键名
   * @param {*} defaultValue - 默认值
   * @param {boolean} sync - 是否使用同步方法，默认true
   * @returns {Promise<*>} 存储的数据
   */
  static getItem(key, defaultValue = null, sync = true) {
    if (!key || typeof key !== 'string') {
      console.error('StorageUtil.getItem: key must be a non-empty string');
      return Promise.resolve(defaultValue);
    }

    try {
      if (sync) {
        const data = wx.getStorageSync(key);
        return Promise.resolve(data || defaultValue);
      } else {
        return new Promise((resolve) => {
          wx.getStorage({
            key,
            success: (res) => resolve(res.data || defaultValue),
            fail: () => resolve(defaultValue)
          });
        });
      }
    } catch (error) {
      console.error('StorageUtil.getItem error:', error);
      return Promise.resolve(defaultValue);
    }
  }

  /**
   * 删除本地存储的数据
   * @param {string} key - 存储键名
   * @param {boolean} sync - 是否使用同步方法，默认true
   * @returns {Promise<boolean>} 删除是否成功
   */
  static removeItem(key, sync = true) {
    if (!key || typeof key !== 'string') {
      console.error('StorageUtil.removeItem: key must be a non-empty string');
      return Promise.resolve(false);
    }

    try {
      if (sync) {
        wx.removeStorageSync(key);
        return Promise.resolve(true);
      } else {
        return new Promise((resolve) => {
          wx.removeStorage({
            key,
            success: () => resolve(true),
            fail: (error) => {
              console.error('StorageUtil.removeItem failed:', error);
              resolve(false);
            }
          });
        });
      }
    } catch (error) {
      console.error('StorageUtil.removeItem error:', error);
      return Promise.resolve(false);
    }
  }

  /**
   * 清空所有本地存储
   * @param {boolean} sync - 是否使用同步方法，默认true
   * @returns {Promise<boolean>} 清空是否成功
   */
  static clear(sync = true) {
    try {
      if (sync) {
        wx.clearStorageSync();
        return Promise.resolve(true);
      } else {
        return new Promise((resolve) => {
          wx.clearStorage({
            success: () => resolve(true),
            fail: (error) => {
              console.error('StorageUtil.clear failed:', error);
              resolve(false);
            }
          });
        });
      }
    } catch (error) {
      console.error('StorageUtil.clear error:', error);
      return Promise.resolve(false);
    }
  }

  /**
   * 获取本地存储信息
   * @param {boolean} sync - 是否使用同步方法，默认true
   * @returns {Promise<Object>} 存储信息 {keys: Array, currentSize: number, limitSize: number}
   */
  static getStorageInfo(sync = true) {
    try {
      if (sync) {
        const info = wx.getStorageInfoSync();
        return Promise.resolve(info);
      } else {
        return new Promise((resolve) => {
          wx.getStorageInfo({
            success: (res) => resolve(res),
            fail: (error) => {
              console.error('StorageUtil.getStorageInfo failed:', error);
              resolve({ keys: [], currentSize: 0, limitSize: 0 });
            }
          });
        });
      }
    } catch (error) {
      console.error('StorageUtil.getStorageInfo error:', error);
      return Promise.resolve({ keys: [], currentSize: 0, limitSize: 0 });
    }
  }

  /**
   * 检查某个键是否存在
   * @param {string} key - 存储键名
   * @returns {Promise<boolean>} 是否存在
   */
  static async hasKey(key) {
    try {
      const info = await this.getStorageInfo();
      return info.keys.includes(key);
    } catch (error) {
      console.error('StorageUtil.hasKey error:', error);
      return false;
    }
  }

  /**
   * 批量设置数据
   * @param {Object} data - 要设置的数据对象
   * @returns {Promise<boolean>} 是否全部设置成功
   */
  static async setBatch(data) {
    if (!data || typeof data !== 'object') {
      console.error('StorageUtil.setBatch: data must be an object');
      return false;
    }

    try {
      const promises = Object.keys(data).map(key => 
        this.setItem(key, data[key])
      );
      
      const results = await Promise.all(promises);
      return results.every(result => result === true);
    } catch (error) {
      console.error('StorageUtil.setBatch error:', error);
      return false;
    }
  }

  /**
   * 批量获取数据
   * @param {Array<string>} keys - 要获取的键名数组
   * @param {*} defaultValue - 默认值
   * @returns {Promise<Object>} 获取的数据对象
   */
  static async getBatch(keys, defaultValue = null) {
    if (!Array.isArray(keys)) {
      console.error('StorageUtil.getBatch: keys must be an array');
      return {};
    }

    try {
      const promises = keys.map(key => 
        this.getItem(key, defaultValue).then(value => ({ [key]: value }))
      );
      
      const results = await Promise.all(promises);
      return Object.assign({}, ...results);
    } catch (error) {
      console.error('StorageUtil.getBatch error:', error);
      return {};
    }
  }

  /**
   * 批量删除数据
   * @param {Array<string>} keys - 要删除的键名数组
   * @returns {Promise<boolean>} 是否全部删除成功
   */
  static async removeBatch(keys) {
    if (!Array.isArray(keys)) {
      console.error('StorageUtil.removeBatch: keys must be an array');
      return false;
    }

    try {
      const promises = keys.map(key => this.removeItem(key));
      const results = await Promise.all(promises);
      return results.every(result => result === true);
    } catch (error) {
      console.error('StorageUtil.removeBatch error:', error);
      return false;
    }
  }

  /**
   * 获取用户相关的存储数据
   * @returns {Promise<Object>} 用户数据
   */
  static async getUserData() {
    const userKeys = ['userInfo', 'authToken', 'currentCompany', 'companies'];
    return await this.getBatch(userKeys);
  }

  /**
   * 清除用户相关的存储数据
   * @returns {Promise<boolean>} 是否清除成功
   */
  static async clearUserData() {
    const userKeys = ['userInfo', 'authToken', 'currentCompany', 'companies'];
    return await this.removeBatch(userKeys);
  }

  /**
   * 设置用户信息
   * @param {Object} userInfo - 用户信息
   * @param {string} token - 认证token
   * @param {Array} companies - 公司列表
   * @param {Object} currentCompany - 当前公司
   * @returns {Promise<boolean>} 是否设置成功
   */
  static async setUserData(userInfo, token, companies, currentCompany) {
    const userData = {};
    
    if (userInfo) userData.userInfo = userInfo;
    if (token) userData.authToken = token;
    if (companies) userData.companies = companies;
    if (currentCompany) userData.currentCompany = currentCompany;
    
    return await this.setBatch(userData);
  }
}

module.exports = StorageUtil;
