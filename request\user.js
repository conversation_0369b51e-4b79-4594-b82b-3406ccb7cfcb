//用户模块的request请求
const http = require('../utils/http');
const requestModule = require('../request/common');
const util = require('../utils/util');
const { OpenId } = require('../config/common')


const login = (rescode)=>{
    http(requestModule.GetWXOpenId,{
        data:{
            code:rescode
        }
    }).then(
        res=>{
              util.setStorage(OpenId,res.data.data.openid);
              console.log('OpenId获取成功，跳转到登录页面');
              // 获取OpenId成功后，跳转到登录页面进行用户信息绑定
              wx.redirectTo({
                  url: '../Login/Login',
              })
        }
    ).catch(
        err=>{
            console.log('user-err',err);
            // 获取OpenId失败时，显示错误提示
            wx.showToast({
                title: '登录失败，请重试',
                icon: 'none'
            });
        }
    )
}

// 不跳转的登录方法，用于Login页面内部获取OpenId
const loginWithoutRedirect = (rescode)=>{
    http(requestModule.GetWXOpenId,{
        data:{
            code:rescode
        }
    }).then(
        res=>{
              util.setStorage(OpenId,res.data.data.openid);
              console.log('OpenId获取成功');
        }
    ).catch(
        err=>{
            console.log('user-err',err);
            // 获取OpenId失败时，显示错误提示
            wx.showToast({
                title: '获取OpenId失败，请重试',
                icon: 'none'
            });
        }
    )
}

module.exports={
    login,
    loginWithoutRedirect
}