{"description": "项目配置文件", "packOptions": {"ignore": [{"type": "folder", "value": "server"}, {"type": "folder", "value": "scripts"}, {"type": "suffix", "value": ".md"}, {"type": "suffix", "value": ".txt"}], "include": []}, "setting": {"ignoreDevUnusedFiles": false, "ignoreUploadUnusedFiles": false, "bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "minifyWXML": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "libVersion": "2.19.4", "appid": "wx749f8e33378c6aa2", "projectname": "miniprogram-92", "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "simulatorPluginLibVersion": {}}