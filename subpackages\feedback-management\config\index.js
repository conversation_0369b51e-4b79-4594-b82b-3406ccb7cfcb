/**
 * 小程序统一配置管理
 * 整合所有配置项，提供统一的配置接口
 */

// 环境配置
const ENVIRONMENTS = {
  development: {
    SERVER_IP: "localhost",
    SERVER_PORT: 8081,
    DEBUG: true,
    LOG_LEVEL: 'debug'
  },
  iis_development: {
    SERVER_IP: "*************",
    SERVER_PORT: 8081,
    DEBUG: true,
    LOG_LEVEL: 'debug'
  },
  iis_development_https: {
    SERVER_IP: "*************",
    SERVER_PORT: 443,
    DEBUG: true,
    LOG_LEVEL: 'debug',
    USE_HTTPS: true
  },
  production: {
    SERVER_IP: "*************",
    SERVER_PORT: 443,
    DEBUG: false,
    LOG_LEVEL: 'error',
    USE_HTTPS: true
  }
};

// 当前环境 - 可以通过手动修改来切换（微信小程序环境不支持process.env）
const CURRENT_ENV = 'iis_development'; // 可选值: 'development', 'iis_development', 'iis_development_https', 'production'
const currentEnvConfig = ENVIRONMENTS[CURRENT_ENV] || ENVIRONMENTS.development;

/**
 * 主配置对象
 */
const CONFIG = {
  // 当前环境
  ENVIRONMENT: CURRENT_ENV,

  // 服务器配置
  SERVER_IP: currentEnvConfig.SERVER_IP,
  SERVER_PORT: currentEnvConfig.SERVER_PORT,
  USE_HTTPS: currentEnvConfig.USE_HTTPS || false,

  // 完整的服务器地址
  get SERVER_URL() {
    const protocol = this.USE_HTTPS ? 'https' : 'http';
    const port = (this.USE_HTTPS && this.SERVER_PORT === 443) ||
                 (!this.USE_HTTPS && this.SERVER_PORT === 80) ? '' : `:${this.SERVER_PORT}`;
    return `${protocol}://${this.SERVER_IP}${port}`;
  },

  // 调试配置
  DEBUG: currentEnvConfig.DEBUG,
  LOG_LEVEL: currentEnvConfig.LOG_LEVEL,

  // API配置
  API: {
    TIMEOUT: 60000, // 60秒超时
    RETRY_COUNT: 3, // 重试次数
    BASE_PATH: '/api'
  },

  // 获取API完整URL
  get API_BASE_URL() {
    return `${this.SERVER_URL}${this.API.BASE_PATH}`;
  },

  // 认证配置
  AUTH: {
    TOKEN_KEY: 'authToken',
    USER_INFO_KEY: 'userInfo',
    TOKEN_EXPIRE_TIME: 24 * 60 * 60 * 1000 // 24小时
  },

  // 存储配置
  STORAGE: {
    PREFIX: 'feedback_',
    KEYS: {
      USER_INFO: 'userInfo',
      AUTH_TOKEN: 'authToken',
      CURRENT_COMPANY: 'currentCompany',
      COMPANIES: 'companies'
    }
  },

  // 文件上传配置
  UPLOAD: {
    MAX_IMAGE_SIZE: 5 * 1024 * 1024,    // 5MB
    MAX_VIDEO_SIZE: 100 * 1024 * 1024,  // 100MB
    ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    ALLOWED_VIDEO_TYPES: ['mp4', 'avi', 'mov', 'wmv', 'flv']
  },

  // 缓存配置
  CACHE: {
    USER_INFO_KEY: 'userInfo',
    AUTH_TOKEN_KEY: 'authToken',
    CURRENT_COMPANY_KEY: 'currentCompany',
    COMPANIES_KEY: 'companies',
    EXPIRE_TIME: 24 * 60 * 60 * 1000 // 24小时
  },

  // 分页配置
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 50
  },

  // UI配置
  UI: {
    LOADING_DELAY: 300,     // 加载延迟显示时间
    TOAST_DURATION: 2000,   // 提示显示时间
    DEBOUNCE_DELAY: 500     // 防抖延迟时间
  },

  // 业务配置
  BUSINESS: {
    // 任务状态
    TASK_STATUS: {
      ACTIVE: 1,
      INACTIVE: 0
    },

    // 供应状态 - 根据X_ppProduceOrder表的X_SupplyState字段
    SUPPLY_STATUS: {
      PENDING: 0,      // 待供
      SUPPLYING: 1,    // 正供
      COMPLETED: 2,    // 供毕
      CANCELLED: 3     // 作废
    },

    // 供应状态文本映射
    SUPPLY_STATUS_TEXT: {
      0: '待供',
      1: '正供',
      2: '供毕',
      3: '作废'
    },

    // 供应状态样式类映射
    SUPPLY_STATUS_CLASS: {
      0: 'pending',
      1: 'supplying',
      2: 'completed',
      3: 'cancelled'
    },

    // 文件类型
    FILE_TYPES: {
      IMAGE: 'image',
      VIDEO: 'video',
      AUDIO: 'audio'
    }
  },

  // 网络配置
  NETWORK: {
    TIMEOUT: 10000,         // 请求超时时间
    RETRY_COUNT: 3,         // 重试次数
    RETRY_DELAY: 1000       // 重试延迟
  },

  // 安全配置
  SECURITY: {
    TOKEN_HEADER: 'Authorization',
    TOKEN_PREFIX: 'Bearer '
  },

  // 错误码配置
  ERROR_CODES: {
    NETWORK_ERROR: 'NETWORK_ERROR',
    AUTH_FAILED: 'AUTH_FAILED',
    PERMISSION_DENIED: 'PERMISSION_DENIED',
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    SERVER_ERROR: 'SERVER_ERROR'
  },

  // 页面路径配置
  PAGES: {
    LOGIN: '/pages/login/login',
    INDEX: '/pages/index/index',
    PROFILE: '/pages/profile/profile',

    // 子包页面
    PROJECT_LIST: '/subpackages/project-management/project-list/project-list',

    TASK_LIST: '/subpackages/task-management/task-list/task-list',

    FEEDBACK: '/subpackages/feedback-management/feedback/feedback',
    FEEDBACK_LIST: '/pages/feedback-list/feedback-list',
    FEEDBACK_DETAIL: '/subpackages/feedback-management/feedback-detail/feedback-detail',
    CAMERA_CAPTURE: '/subpackages/feedback-management/camera-capture/camera-capture'
  },

  // 图标配置
  ICONS: {
    HOMEPAGE: '/static/homepage.png',
    HOMEPAGE_FILL: '/static/homepage_fill.png',
    PEOPLE: '/static/people.png',
    PEOPLE_FILL: '/static/people_fill.png'
  },

  // 地图配置
  MAP: {
    // 天地图代理配置
    tiandituUseProxy: true,
    tiandituGeocoderUrl: 'http://api.tianditu.gov.cn/geocoder',

    // 坐标系统配置
    coordinateSystem: {
      enableSmartConversion: true,
      defaultCoordType: 'gcj02',
      tiandituCoordType: 'auto'
    }
  }
};

/**
 * 工具方法
 */
CONFIG.utils = {
  /**
   * 获取完整的API地址
   * @param {string} path - API路径
   * @returns {string} 完整的API地址
   */
  getApiUrl(path) {
    const cleanPath = path.startsWith('/') ? path : '/' + path;
    return CONFIG.API_BASE_URL + cleanPath;
  },

  /**
   * 检查是否为开发环境
   * @returns {boolean}
   */
  isDevelopment() {
    return CONFIG.ENVIRONMENT === 'development';
  },

  /**
   * 检查是否为生产环境
   * @returns {boolean}
   */
  isProduction() {
    return CONFIG.ENVIRONMENT === 'production';
  }
};

module.exports = CONFIG;
