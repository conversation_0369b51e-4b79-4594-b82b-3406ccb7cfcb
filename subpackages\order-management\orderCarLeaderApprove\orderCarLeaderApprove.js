const requestModule = require('../../../request/common');
const http = require('../../../utils/http');
const utils = require('../../../utils/util');
Page({

    /**
     * 页面的初始数据
     */
    data: {
        CurrentBillNo:'',
        IsApprove:1,
        FixState:1
    },
    radioChange(e) {
        console.log(e.detail.value);
        if(e.detail.value=="agree")
        {
            this.data.IsApprove=1;
            this.data.FixState=1;
        }
        else
        {
            this.data.FixState=0
            this.data.IsApprove=2;
        }
    },
    //实时获取弹窗的输入的值
    bindKeyInput(e) {
         console.log(e.detail.value)
    },
    openNews(){
      },
    // 取消
    hideCover() {
        wx.navigateBack();
    },

    // 同意
    showCover() {
      
    },
    // 点击确定后的事件处理 获取确认后的用户信息 并作出相应的处理
    formSubmit: function (e) {
        console.log(e);
          console.log(this.data.FixState);
         http(requestModule.UpdateFixState, {
                    data: {
                        ProgId: "X_CarServApply",
                        BillNo: this.data.CurrentBillNo,
                        FixState:this.data.FixState,
                        ApprovalOpinions:e.detail.value.ApprovalOpinions,
                        IsApprove:this.data.IsApprove
                    }
                }).then(res=>{
                    if(res.data.status=="suc")
                    {
                        let intitle="提示"
                        if(this.data.IsApprove==2)
                        {
                            intitle="已否决"
                        }
                        else if(this.data.IsApprove==1)
                        {
                            intitle="已审核"
                        }
                        wx.showModal({
                          title: intitle,
                          content: res.data.data,
                          showCancel:false,
                          complete: (res) => {
                            wx.navigateTo({
                                url: '../order/order',
                            })
                          }
                        })
                    }
                    else
                    {
                        wx.showModal({
                          title: '失败',
                          content: res.data.errors.errorMsg,
                          showCancel:false
                        })
                    }
                }).catch(err=>{
                    console.log("失败信息",err);
                })
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.data.CurrentBillNo = options.BillNo;
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {

    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {

    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {

    }
})
