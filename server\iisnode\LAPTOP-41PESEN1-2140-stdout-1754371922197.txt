上传目录结构初始化完成
[2025-08-05 05:32:16] [SUCCESS] SQL Server连接池创建成功 | {"server":"192.168.16.94","database":"NBSTEST"}
[2025-08-05 05:32:16] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:32:16] [SUCCESS] SQL Server数据库连接测试成功 | {"server":"192.168.16.94","database":"NBSTEST"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:32:16] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:32:16] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:32:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:32:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:32:31] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":68}
[2025-08-05 05:32:31] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:33:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:33:02] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-08-05 05:35:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:35:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:35:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-08-05 05:35:13] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:35:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:35:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:35:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:35:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:35:21] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":6062,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
[2025-08-05 05:35:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:36:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:36:17] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:36:18] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":69}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:36:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:36:20] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:36:20] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-08-05 05:36:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:36:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:36:28] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":6063,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
[2025-08-05 05:36:28] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:38:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:38:01] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:38:04] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:38:04] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:38:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:38:05] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:38:05] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:38:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:38:06] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:38:06] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:38:08] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:38:08] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:38:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:38:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:38:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:38:09] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":70}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:38:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:38:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
天地图代理请求: 纬度=24.531118435329862, 经度=118.1821609157986
天地图API响应: {
  result: {
    formatted_address: '福建省厦门市湖里区金山街道泗水道617-101',
    location: { lon: 118.182161, lat: 24.531118 },
    addressComponent: {
      address: '泗水道617-101',
      town: '金山街道',
      nation: '中国',
      city: '厦门市',
      county_code: '*********',
      poi_position: '东北',
      county: '湖里区',
      city_code: '*********',
      address_position: '东北',
      poi: '美宜佳F389',
      province_code: '*********',
      town_code: '*********005',
      province: '福建省',
      road: '木浦路',
      road_distance: 119,
      address_distance: 41,
      poi_distance: 41
    }
  },
  msg: 'ok',
  status: '0'
}
[2025-08-05 05:38:21] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:38:37] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:38:37] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:38:37] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:38:37] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:38:37] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":6064,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
[2025-08-05 05:38:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:39:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:39:03] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-08-05 05:39:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:39:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:45:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:45:06] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:45:09] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:45:09] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:45:09] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:45:09] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:45:09] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:45:10] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:45:11] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:45:11] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:45:13] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:45:13] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:46:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:46:41] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:46:44] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:46:44] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:46:44] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:46:45] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:46:45] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:46:45] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:46:46] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:46:46] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:46:48] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:46:48] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:46:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:46:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:46:49] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":71}
[2025-08-05 05:46:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:46:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:46:50] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
天地图代理请求: 纬度=24.531118435329862, 经度=118.1821609157986
天地图API响应: {
  result: {
    formatted_address: '福建省厦门市湖里区金山街道泗水道617-101',
    location: { lon: 118.182161, lat: 24.531118 },
    addressComponent: {
      address: '泗水道617-101',
      town: '金山街道',
      nation: '中国',
      city: '厦门市',
      county_code: '*********',
      poi_position: '东北',
      county: '湖里区',
      city_code: '*********',
      address_position: '东北',
      poi: '美宜佳F389',
      province_code: '*********',
      town_code: '*********005',
      province: '福建省',
      road: '木浦路',
      road_distance: 119,
      address_distance: 41,
      poi_distance: 41
    }
  },
  msg: 'ok',
  status: '0'
}
[2025-08-05 05:47:00] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:47:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:47:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:47:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:47:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:47:14] [INFO] 现场信息反馈记录创建成功 | {"feedbackId":6065,"task_number":"B05-2200252","feedback_user_id":"2023043","location_status":"authorized"}
[2025-08-05 05:47:14] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:47:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:47:20] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2023043 公司ID: 1007
[2025-08-05 05:47:20] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2023043 公司ID: 1007
[2025-08-05 05:47:20] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:47:20] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:47:20] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2023043 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 64
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 64
[2025-08-05 05:47:20] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2023043:1007","ttl":120000}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 64
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 64
[2025-08-05 05:47:20] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2023043:1007","ttl":120000}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 64
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 64
[2025-08-05 05:47:20] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2023043:1007","ttl":120000}
[2025-08-05 05:47:20] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:47:20] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:47:22] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:47:22] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:47:30] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:47:31] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:47:31] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:49:19] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:49:19] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:49:22] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:49:22] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:49:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:49:22] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:49:22] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:49:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:49:24] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:49:24] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:49:25] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:49:25] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:49:25] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:49:25] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:49:25] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:49:25] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":72}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:49:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:49:26] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-08-05 05:50:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:50:24] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:50:26] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:50:26] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:52:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1003"}
=== 工程列表API调用 ===
查询公司ID: 1003
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:52:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1003"}
=== 工程列表API调用 ===
查询公司ID: 1003
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:52:34] [DEBUG] 项目列表查询成功 | {"totalProjects":0,"filteredProjects":0,"sampleProject":null}
[2025-08-05 05:52:34] [DEBUG] 原始表查询成功 | {"totalRows":0,"filteredRows":0,"companyId":"1003"}
[2025-08-05 05:52:34] [DEBUG] 项目列表查询成功 | {"totalProjects":0,"filteredProjects":0,"sampleProject":null}
[2025-08-05 05:52:34] [DEBUG] 原始表查询成功 | {"totalRows":0,"filteredRows":0,"companyId":"1003"}
[2025-08-05 05:52:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1003"}
=== 工程列表API调用 ===
查询公司ID: 1003
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:52:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1003"}
=== 工程列表API调用 ===
查询公司ID: 1003
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:52:34] [DEBUG] 项目列表查询成功 | {"totalProjects":0,"filteredProjects":0,"sampleProject":null}
[2025-08-05 05:52:34] [DEBUG] 原始表查询成功 | {"totalRows":0,"filteredRows":0,"companyId":"1003"}
[2025-08-05 05:52:34] [DEBUG] 项目列表查询成功 | {"totalProjects":0,"filteredProjects":0,"sampleProject":null}
[2025-08-05 05:52:34] [DEBUG] 原始表查询成功 | {"totalRows":0,"filteredRows":0,"companyId":"1003"}
[2025-08-05 05:52:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1003"}
=== 工程列表API调用 ===
查询公司ID: 1003
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:52:47] [DEBUG] 项目列表查询成功 | {"totalProjects":0,"filteredProjects":0,"sampleProject":null}
[2025-08-05 05:52:47] [DEBUG] 原始表查询成功 | {"totalRows":0,"filteredRows":0,"companyId":"1003"}
[2025-08-05 05:52:47] [DEBUG] 解析当前公司信息成功 | {"companyId":"1003"}
=== 工程列表API调用 ===
查询公司ID: 1003
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:52:47] [DEBUG] 项目列表查询成功 | {"totalProjects":0,"filteredProjects":0,"sampleProject":null}
[2025-08-05 05:52:47] [DEBUG] 原始表查询成功 | {"totalRows":0,"filteredRows":0,"companyId":"1003"}
[2025-08-05 05:53:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1003"}
=== 工程列表API调用 ===
查询公司ID: 1003
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:53:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1003"}
=== 工程列表API调用 ===
查询公司ID: 1003
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:53:05] [DEBUG] 项目列表查询成功 | {"totalProjects":0,"filteredProjects":0,"sampleProject":null}
[2025-08-05 05:53:05] [DEBUG] 原始表查询成功 | {"totalRows":0,"filteredRows":0,"companyId":"1003"}
[2025-08-05 05:53:05] [DEBUG] 项目列表查询成功 | {"totalProjects":0,"filteredProjects":0,"sampleProject":null}
[2025-08-05 05:53:05] [DEBUG] 原始表查询成功 | {"totalRows":0,"filteredRows":0,"companyId":"1003"}
[2025-08-05 05:53:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1003"}
=== 工程列表API调用 ===
查询公司ID: 1003
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:53:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1003"}
=== 工程列表API调用 ===
查询公司ID: 1003
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:53:05] [DEBUG] 项目列表查询成功 | {"totalProjects":0,"filteredProjects":0,"sampleProject":null}
[2025-08-05 05:53:05] [DEBUG] 原始表查询成功 | {"totalRows":0,"filteredRows":0,"companyId":"1003"}
[2025-08-05 05:53:05] [DEBUG] 项目列表查询成功 | {"totalProjects":0,"filteredProjects":0,"sampleProject":null}
[2025-08-05 05:53:05] [DEBUG] 原始表查询成功 | {"totalRows":0,"filteredRows":0,"companyId":"1003"}
[2025-08-05 05:53:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:53:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:53:34] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:53:34] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:53:34] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:53:34] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:53:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:53:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:53:34] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:53:34] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:53:34] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:53:34] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:53:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:53:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:53:38] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:53:38] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:53:38] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:53:38] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:53:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:53:38] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:53:38] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:53:38] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:53:38] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:53:38] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:53:48] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-08-05 05:53:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-08-05 05:53:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
[2025-08-05 05:53:49] [DEBUG] 工程统计信息查询成功 | {"total_tasks":0,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":0}
[2025-08-05 05:53:52] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:53:52] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:53:52] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:54:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:54:05] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:54:07] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:54:07] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:54:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:54:08] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:54:08] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:54:08] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:54:09] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:54:09] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:54:11] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:54:11] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:54:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2023043 公司ID: 1007
[2025-08-05 05:54:27] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
开始获取分类反馈单数据，用户ID: 2023043 公司ID: 1007
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 64
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 64
[2025-08-05 05:54:27] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2023043:1007","ttl":120000}
工程查询结果行数: 1
任务单查询结果行数: 1
用户反馈查询结果行数: 64
=== Feedback.getGroupedByUserId 查询结果 ===
工程数量: 1
总任务单数量: 1
总反馈数量: 64
[2025-08-05 05:54:27] [DEBUG] 缓存设置成功 | {"key":"grouped_feedback:2023043:1007","ttl":120000}
[2025-08-05 05:54:33] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:54:34] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:54:34] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:55:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:55:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:55:04] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:55:04] [DEBUG] 工程统计信息查询成功 | {"total_tasks":1,"supplying_tasks":0,"completed_tasks":0,"total_feedbacks":72}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:55:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:55:07] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
天地图代理请求: 纬度=24.531118435329862, 经度=118.1821609157986
天地图API响应: {
  result: {
    formatted_address: '福建省厦门市湖里区金山街道泗水道617-101',
    location: { lon: 118.182161, lat: 24.531118 },
    addressComponent: {
      address: '泗水道617-101',
      town: '金山街道',
      nation: '中国',
      city: '厦门市',
      county_code: '*********',
      poi_position: '东北',
      county: '湖里区',
      city_code: '*********',
      address_position: '东北',
      poi: '美宜佳F389',
      province_code: '*********',
      town_code: '*********005',
      province: '福建省',
      road: '木浦路',
      road_distance: 119,
      address_distance: 41,
      poi_distance: 41
    }
  },
  msg: 'ok',
  status: '0'
}
[2025-08-05 05:55:18] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:55:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
[2025-08-05 05:55:22] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
⚠️ feedbacks表不存在，返回空反馈记录
[2025-08-05 05:55:34] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== Task.getByProjectId 调试信息 ===
任务 1: B05-2200252
  scheduled_time: Sun Jan 09 2022 08:00:00 GMT+0800 (中国标准时间)
  scheduled_time类型: object
  scheduled_time构造函数: Date
  getTime(): 1641686400000
[2025-08-05 05:56:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:56:49] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:56:49] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:56:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:56:49] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:56:49] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:56:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:56:49] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:56:49] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:56:49] [DEBUG] 解析当前公司信息成功 | {"companyId":"1004"}
=== 工程列表API调用 ===
查询公司ID: 1004
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:56:49] [DEBUG] 项目列表查询成功 | {"totalProjects":1,"filteredProjects":1,"sampleProject":{"id":"B03AJA210001","name":"厦门滨海凯悦酒店-A2地块","task_count":0}}
[2025-08-05 05:56:49] [DEBUG] 原始表查询成功 | {"totalRows":1,"filteredRows":1,"companyId":"1004"}
[2025-08-05 05:56:54] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:56:56] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:56:56] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:56:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: {}
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [],
  willUsePagination: false
}
[2025-08-05 05:56:56] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:56:58] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:56:58] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:56:59] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:56:59] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
[2025-08-05 05:56:59] [DEBUG] 解析当前公司信息成功 | {"companyId":"1007"}
=== 工程列表API调用 ===
查询公司ID: 1007
查询过滤条件: {}
原始查询参数: { keyword: '' }
解析后分页参数: { page: 1, pageSize: 10, pageType: 'number', pageSizeType: 'number' }
分页检查结果: {
  hasPage: false,
  pageValue: undefined,
  pageSizeValue: undefined,
  needPagination: false,
  queryKeys: [ 'keyword' ],
  willUsePagination: false
}
[2025-08-05 05:57:01] [DEBUG] 项目列表查询成功 | {"totalProjects":395,"filteredProjects":395,"sampleProject":{"id":"B05AJA16148","name":"厦门高崎国际T4航站楼和航空港物流中心工程","task_count":0}}
[2025-08-05 05:57:01] [DEBUG] 原始表查询成功 | {"totalRows":395,"filteredRows":395,"companyId":"1007"}
