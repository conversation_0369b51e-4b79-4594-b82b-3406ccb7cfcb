// pages/feedback-detail/feedback-detail.js

Page({
  data: {
    feedback: null,
    media: [],
    images: [],
    videos: [],
    audios: [],
    loading: true,
    currentPlayingIndex: -1,
    audioContext: null,

  },

  onLoad(options) {
    this.checkLogin();



    if (options.id) {
      this.loadFeedbackDetail(options.id);
    } else {
      wx.showToast({
        title: "参数错误",
        icon: "none",
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  onUnload() {
    // 清理音频播放
    this.stopAllAudio();
  },

  // 检查登录状态
  checkLogin() {
    const userInfo = wx.getStorageSync("userInfo");
    const currentCompany = wx.getStorageSync("currentCompany");

    if (!userInfo) {
      wx.redirectTo({
        url: "/pages/login/login",
      });
      return;
    }

    if (!currentCompany) {
      wx.showToast({
        title: "请先选择公司",
        icon: "none",
      });
      setTimeout(() => {
        wx.switchTab({
          url: "/pages/index/index",
        });
      }, 2000);
      return;
    }
  },

  // 加载现场信息反馈记录详情
  async loadFeedbackDetail(id) {
    try {
      this.setData({ loading: true });
      const app = getApp();

      const res = await app.request({
        url: `/api/feedbacks/${id}`,
        method: "GET",
      });

      if (res.data.success) {
        const feedback = {
          ...res.data.data.feedback,
          feedback_time_text: this.formatDateTime(
            res.data.data.feedback.feedback_time
          ),
        };

        const app = getApp();

        // 处理媒体文件数据
        const media = res.data.data.media.map((item, index) => {
          let fullPath = '';

          // 如果file_path存在且不为空
          if (item.file_path) {
            // 如果已经是完整的HTTP URL，直接使用
            if (item.file_path.startsWith("http://") || item.file_path.startsWith("https://")) {
              fullPath = item.file_path;
            }
            // 如果是以/api/files/开头的路径，拼接baseUrl
            else if (item.file_path.startsWith("/api/files/")) {
              fullPath = `${app.globalData.baseUrl}${item.file_path}`;
            }
            // 如果是以/upload/开头的路径，直接拼接baseUrl
            else if (item.file_path.startsWith("/upload/")) {
              fullPath = `${app.globalData.baseUrl}${item.file_path}`;
            }
            // 如果是相对路径，尝试不同的前缀
            else {
              // 首先尝试/api/files/前缀
              fullPath = `${app.globalData.baseUrl}/api/files/${item.file_path}`;
            }
          } else {
            console.warn(`媒体文件 ${item.file_name} 的路径为空`);
          }

          return {
            ...item,
            index,
            isPlaying: false,
            file_size_text: this.formatFileSize(item.file_size),
            duration_text: this.formatDuration(item.duration),
            file_path: fullPath,
          };
        });

        // 按文件类型分组
        const images = media.filter((item) => item.file_type === "image");
        const videos = media.filter((item) => item.file_type === "video");
        const audios = media.filter((item) => item.file_type === "audio");



        this.setData({
          feedback,
          media,
          images,
          videos,
          audios,
          loading: false,
        });

        // 更新页面标题
        wx.setNavigationBarTitle({
          title: `现场信息反馈记录详情`,
        });
      } else {
        wx.showToast({
          title: res.data.message || "加载失败",
          icon: "none",
        });
        this.setData({ loading: false });
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      }
    } catch (error) {
      console.error("加载现场信息反馈记录详情失败:", error);
      wx.showToast({
        title: "网络错误",
        icon: "none",
      });
      this.setData({ loading: false });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    }
  },

  // 格式化日期时间
  formatDateTime(dateTimeStr) {
    const Formatter = require('../utils/formatter');
    return Formatter.formatDateTime(dateTimeStr, 'YYYY-MM-DD HH:mm:ss');
  },

  // 预览图片
  onPreviewImage(e) {
    const src = e.currentTarget.dataset.src;
    const images = this.data.images.map((item) => item.file_path);

    wx.previewImage({
      current: src,
      urls: images,
    });
  },

  // 图片加载成功
  onImageLoad(e) {
    console.log("图片加载成功:", e.detail);
  },







  // 图片加载失败
  onImageError(e) {
    const src = e.currentTarget.dataset.src;
    console.error("图片加载失败:", e.detail, "URL:", src);

    // 尝试备用路径
    const app = getApp();
    const originalSrc = src;

    // 如果当前是/api/files/路径，尝试/upload/路径
    if (originalSrc && originalSrc.includes('/api/files/')) {
      const backupSrc = originalSrc.replace('/api/files/', '/upload/');
      console.log("尝试备用路径:", backupSrc);

      // 更新图片源
      const dataset = e.currentTarget.dataset;
      if (dataset.index !== undefined) {
        const imageIndex = this.data.images.findIndex(img => img.file_path === originalSrc);
        if (imageIndex !== -1) {
          this.setData({
            [`images[${imageIndex}].file_path`]: backupSrc
          });
          return; // 不显示错误提示，让它重新尝试加载
        }
      }
    }

    // 移除图片加载失败的弹出提示
    // wx.showToast({
    //   title: "图片加载失败",
    //   icon: "none",
    //   duration: 2000
    // });
  },

  // 播放/暂停音频
  onPlayAudio(e) {
    const index = e.currentTarget.dataset.index;
    const audioItem = this.data.audios.find((item) => item.index === index);

    if (!audioItem) {
      console.error("音频项未找到:", index);
      return;
    }

    if (this.data.currentPlayingIndex === index) {
      // 暂停当前播放的音频
      this.pauseAudio(index);
    } else {
      // 停止其他音频，播放当前音频
      this.stopAllAudio();
      this.playAudio(index, audioItem.file_path);
    }
  },

  // 播放音频
  playAudio(index, src) {
    // 确保先清理之前的音频上下文
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }

    const audioContext = wx.createInnerAudioContext();
    audioContext.src = src;

    audioContext.onPlay(() => {
      // 找到音频在audios数组中的位置
      const audioIndex = this.data.audios.findIndex(
        (item) => item.index === index
      );
      this.setData({
        currentPlayingIndex: index,
        audioContext,
        [`audios[${audioIndex}].isPlaying`]: true,
      });
    });

    audioContext.onPause(() => {
      // 找到音频在audios数组中的位置
      const audioIndex = this.data.audios.findIndex(
        (item) => item.index === index
      );
      this.setData({
        currentPlayingIndex: -1,
        audioContext: null,
        [`audios[${audioIndex}].isPlaying`]: false,
      });
    });

    audioContext.onEnded(() => {
      // 找到音频在audios数组中的位置
      const audioIndex = this.data.audios.findIndex(
        (item) => item.index === index
      );
      this.setData({
        currentPlayingIndex: -1,
        audioContext: null,
        [`audios[${audioIndex}].isPlaying`]: false,
      });
    });

    audioContext.onError((error) => {
      console.error("音频播放错误:", error);
      wx.showToast({
        title: "音频播放失败",
        icon: "none",
      });
      // 找到音频在audios数组中的位置
      const audioIndex = this.data.audios.findIndex(
        (item) => item.index === index
      );
      this.setData({
        currentPlayingIndex: -1,
        audioContext: null,
        [`audios[${audioIndex}].isPlaying`]: false,
      });
    });

    audioContext.play();
  },

  // 暂停音频
  pauseAudio(index) {
    if (this.data.audioContext) {
      this.data.audioContext.pause();
    }

    // 找到音频在audios数组中的位置
    const audioIndex = this.data.audios.findIndex(
      (item) => item.index === index
    );

    // 重置播放状态
    this.setData({
      currentPlayingIndex: -1,
      audioContext: null,
      [`audios[${audioIndex}].isPlaying`]: false,
    });
  },

  // 停止所有音频
  stopAllAudio() {
    if (this.data.audioContext) {
      this.data.audioContext.stop();
      this.data.audioContext.destroy();
    }

    this.setData({
      currentPlayingIndex: -1,
      audioContext: null,
    });

    // 重置所有音频的播放状态
    const audios = this.data.audios.map((item) => ({
      ...item,
      isPlaying: false,
    }));
    this.setData({ audios });
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  },

  // 格式化时长
  formatDuration(seconds) {
    if (!seconds || seconds <= 0) return "未知";
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  },



});
