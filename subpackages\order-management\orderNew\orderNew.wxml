<!--pages/orderDetail/orderDetail.wxml-->
<form class="CarCard" bindsubmit="formSubmit">
    <view class="inTitle">车辆信息</view>
    <view class="Info">
        <view class="titleinput">
            <view style="display: flex; justify-content:space-between;margin-bottom: 20rpx;">
                <view style="line-height: 60rpx;">车号: </view>
                <view>
                    <button type="primary" size="mini" bindtap="callcamerapage">扫码识别车号</button>
                </view>
            </view>
            <input type="text" name="CarMark" bindblur="outCarMark" value="{{CarMarkValue}}" />
        </view>
        <view style="margin-bottom: 20rpx;">车牌号: {{FromData.CarName}}</view>
        <view class="titleinput">
            <view space="ensp">累计行驶公里数: </view>
            <input type="text" name="TotalMileage" />
        </view>
        <picker mode="selector" range="{{RepairList}}" bindchange="bindPickerValue">        
            <view style="margin-bottom: 10rpx;">
               报修项目: {{RepairList[index]}}
            </view>
            <view class="picker">
            <button type="default" size="mini"> 选择报修项目</button>
            </view>
        </picker>

        <view class="titleinputReason">
            <view space="ensp">维修原因: </view>
            <textarea style="border:1px solid black;width: 100%;height: 300rpx;" name="FixReason"></textarea>>
        </view>
    </view>
    <view class="buttonArea">
        <button type="warn" bindtap="cancleOrder">放弃订单</button>
        <button type="primary" form-type="submit">保存订单</button>
    </view>
</form>